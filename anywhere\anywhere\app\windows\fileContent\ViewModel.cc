#include <cassert>
#include <thread>
#include <chrono>
#include <atomic>
#include <memory>
#include <QProcess>
#include <QStandardPaths>
#include <QTimer>
#include "recover/recover.h"
#include "app/services/CheckerUtils.h"
#include "database/DeletedFilesTable.h"
#include "core/services/DeletedFileCollectionService.h"
#include "app/services/Miscs.h"
#include "Log.h"

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifdef _WIN32
#include <Windows.h>
#undef max
#undef min
#else
#include <regex>
#endif

#include "ViewModel.h"
#include "CancellationToken.h"
#include "../../services/Executors.h"
#include "../../services/Miscs.h"
#include "core/tasks/IndexFileContentTask.h"
#include "core/ServiceRegistry.h"
#include "../../utils/Executors.h"
#include "core/tasks/WaitForLoadIndexDoneTask.h"
#include "TextNormalizer.h"
#include <fmt/core.h>
#include <fmt/format.h>
#include "app/services/FileExtCheckerImpl.h"
#include "app/services/CheckerUtils.h"
using namespace anywhere::core;

namespace anywhere {
namespace app {

ViewModel::ViewModel() : ViewModel(true) {
  // 默认构造函数，需要搜索索引
}

ViewModel::ViewModel(bool needSearchIndex) {
  searchEngineService_ =
      ServiceRegistry::instance()->getService<SearchEngineServiceToken>();
  fileContentIndexService_ =
      ServiceRegistry::instance()->getService<FileContentIndexServiceToken>();
  auto defaultFileParseEngine = ServiceRegistry::instance()->getService<DefaultFileParseEngineToken>();
  defaultFileParseEngine->setFileExtChecker(std::make_shared<FileExtCheckerImpl>());
  configService_ =
      ServiceRegistry::instance()->getService<ConfigServiceToken>();
  licenseService_ =
      ServiceRegistry::instance()->getService<LicenseServiceToken>();
  mainThreadExecutor_ =
      ServiceRegistry::instance()->getService<MainExecutorToken>();
  ioTheadExecutor_ = ServiceRegistry::instance()->getService<IoExecutorToken>();
  deletedFileCollectionService_ =
      ServiceRegistry::instance()->getService<DeletedFileCollectionServiceToken>();
  queryParser_ = ServiceRegistry::instance()->getService<QueryParserToken>();
  fileIconProvider_ =
      ServiceRegistry::instance()->getService<FileIconProviderToken>();
  restartManager_ =
      ServiceRegistry::instance()->getService<RestartManagerToken>();
  // manual_ = ServiceRegistry::instance()->getService<ManualToken>();
  cleanFileService_ =
      ServiceRegistry::instance()->getService<CleanFileServiceToken>();
  searchHistory_ = SearchHistory();

  license_ = std::make_unique<QLicense>(licenseService_->license());
  // setLicenseUnavailable(!licenseService_->licenseAvailable());
  if (needSearchIndex)
  onSearchEngineStateChanged(searchEngineService_->state());
  indexStateConnection_ = searchEngineService_->stateChanged().connect(
      viaExecutor(mainThreadExecutor_)(
          [this, cancellationToken =
                     miscTaskCancellationSource_.getToken()](auto state) {
            if (!cancellationToken.isCancellationRequested())
              onSearchEngineStateChanged(state);
          }));
  indexProgressConnection_ = searchEngineService_->progressChanged().connect(
      viaExecutor(mainThreadExecutor_)(
          [this, cancellationToken =
                     miscTaskCancellationSource_.getToken()](auto progress) {
            if (!cancellationToken.isCancellationRequested())
              onLoadIndexProgressChanged(progress);
          }));

  indexCommitedConnection_ = fileContentIndexService_->indexCommited().connect(
      viaExecutor(mainThreadExecutor_)(
          [this, cancellationToken =
                     miscTaskCancellationSource_.getToken()](auto opstamp) {
            if (!cancellationToken.isCancellationRequested())
              onIndexCommited(opstamp);
          }));
  docIndexedConnection_ = fileContentIndexService_->docIndexed().connect(
      [this, cancellationToken = miscTaskCancellationSource_.getToken()](
          DocId id, const std::filesystem::path &path,
          const std::u8string &text,
          std::optional<core::FileContentIndexService::OpStamp> opstamp, std::optional<SearchEngineService::Document> doc,
          void *context) {
        if (!cancellationToken.isCancellationRequested())
          onDocIndexed(id, path, text, opstamp, doc, context);
      });
  licenseUnavailableConnection_ =
      licenseService_->licenseAvailableChanged().connect(
          viaExecutor(mainThreadExecutor_)(
              [this, cancellationToken =
                         miscTaskCancellationSource_.getToken()](bool v) {
                if (!cancellationToken.isCancellationRequested())
                  setLicenseUnavailable(!v);
              }));
  fileCleanedConnection_ =
      cleanFileService_->fileCleaned().connect(viaExecutor(mainThreadExecutor_)(
          [this, cancellationToken =
                     miscTaskCancellationSource_.getToken()](DocId docId) {
            if (!cancellationToken.isCancellationRequested() && topHits_)
              topHits_->removeHitByDocId(docId);
          }));
  // 只有在需要搜索索引时才加载索引
  if (needSearchIndex && searchEngineService_->state() == SearchEngineService::State::Init) {
    std::vector<std::filesystem::path> roots = searchEngineService_->getRoots();
    searchEngineService_->loadIndex(roots);
  }
  setPendingFilesSet(std::make_unique<QDocSet>(DocSet()));

  // 初始化统一的UI更新定时器
  uiUpdateTimer_ = new QTimer(this);
  uiUpdateTimer_->setInterval(UI_UPDATE_INTERVAL);
  uiUpdateTimer_->setSingleShot(false); // 重复触发，更简单可靠
  connect(uiUpdateTimer_, &QTimer::timeout, this, [this]() {
    performUIUpdate();
  });

  // 优化：确保定时器在对象销毁时停止
  connect(this, &QObject::destroyed, [this]() {
    if (uiUpdateTimer_ && uiUpdateTimer_->isActive()) {
      uiUpdateTimer_->stop();
      SPDLOG_DEBUG("UI update timer stopped on object destruction");
    }
  });
}

void ViewModel::clean(const std::filesystem::path &fileName) {
  cleanFileService_->cleanSync(fileName);
}

ViewModel::~ViewModel() { close(); }

void ViewModel::close() {
  searchTaskCancallationSource_.requestCancellation();
  backgroundIndexFileContentTaskCancallationSource_.requestCancellation();
  miscTaskCancellationSource_.requestCancellation();
}

void ViewModel::restart() { restartManager_->restart(); }

// 解析16进制文件路径
static std::filesystem::path decodePath(const std::string &encodedPath) {
#ifndef _WIN32
  std::stringstream decodedPath;
  for (size_t i = 0; i < encodedPath.length(); ++i) {
    if (encodedPath[i] == '\\' && i + 3 < encodedPath.length() && encodedPath[i + 1] == 'x') {
      std::string hexCode = encodedPath.substr(i + 2, 2);
      decodedPath << core::Utils::numericToChar(hexCode, 16);
      i += 3; // /home/<USER>/\xe4\xb8\xad\xe6\x96\x87\x5c\xe5\x90\x8d\xe7\xa7\xb0
    } else {
      decodedPath << encodedPath[i];
    }
  }
  return std::filesystem::path(decodedPath.str());
#else
  return std::filesystem::path(encodedPath);
#endif
}

void ViewModel::setIndexState(IndexState state) {
  if (indexState_ == state)
    return;
  indexState_ = state;
  Q_EMIT indexStateChanged(indexState_);
}

void ViewModel::setIndexErrorReason(const QString &reason) {
  if (indexErrorReason_ == reason)
    return;
  indexErrorReason_ = reason;
  Q_EMIT indexErrorReasonChanged(indexErrorReason_);
}

void ViewModel::setIndexProgress(float progress) {
  if (indexProgress_ == progress)
    return;
  indexProgress_ = progress;
  Q_EMIT indexProgressChanged(indexProgress_);
}

void ViewModel::setIndexedFilesCnt(unsigned int cnt) {
  if (indexedFilesCnt_ == cnt)
    return;
  indexedFilesCnt_ = cnt;
  Q_EMIT indexedFilesCntChanged(indexedFilesCnt_);
}

void ViewModel::setRoots(const std::vector<std::filesystem::path> &roots) {
  QVector<QString> tmpRoots;
  for (const auto &item : roots) {
    tmpRoots.push_back(
        QString::fromUtf8(reinterpret_cast<const char *>(item.c_str())));
  }

  if (tmpRoots.size() == roots_.size() &&
      std::equal(tmpRoots.begin(), tmpRoots.end(), roots_.begin()))
    return;

  roots_ = tmpRoots;

  Q_EMIT rootsChanged(roots_);
}

void ViewModel::setLicenseUnavailable(bool v) {
  if (licenseUnavailable_ == v)
    return;
  licenseUnavailable_ = v;
  Q_EMIT licenseUnavailableChanged(licenseUnavailable_);
}

void ViewModel::addFileNameHistory(const QString &path) {
  searchHistory_.add(SearchHistory::Type::FileName, path);

  filenamesHistory_ = searchHistory_.filenames();
  Q_EMIT filenamesHistoryChanged(filenamesHistory_);
}

void ViewModel::addFullTextHistory(const QString &path) {
  searchHistory_.add(SearchHistory::Type::FullText, path);

  fullTextsHistory_ = searchHistory_.fullTexts();
  Q_EMIT fullTextsHistoryChanged(fullTextsHistory_);
}

void ViewModel::setSearching(bool v) {
  if (searching_ == v)
    return;
  SPDLOG_DEBUG("set searching: {}", v);
  searching_ = v;
  Q_EMIT searchingChanged(searching_);
}

void ViewModel::setFilterState(int v) {
  {
    std::lock_guard<std::mutex> lock(filterMutex_);
    if (filterState_ == v)
      return;
    if (filterState_ == FilterState::Canceling && v == FilterState::Idle) {
      filterState_ = FilterState::Canceled;
      SPDLOG_DEBUG("set filterState: {}", filterState_);
    } else {
      SPDLOG_DEBUG("set filterState: {}", v);
      filterState_ = v;
    }
  }
  Q_EMIT filterStateChanged(filterState_);
}

void ViewModel::startSearchTime() {
  // 可以多次调用开始
  searchTimer_.start();
}

void ViewModel::endSearchTime() {
  if (!searchTimer_.isValid())
    return;
  searchTime_ = (double)searchTimer_.elapsed() / 1000;
  Q_EMIT searchTimeChanged(searchTime_);
}

void ViewModel::setFileNameQuery(const QString &fileNameQuery) {
  if (fileNameQuery_ == fileNameQuery)
    return;
  fileNameQuery_ = fileNameQuery;
  Q_EMIT fileNameQueryChanged(fileNameQuery_);
}

void ViewModel::setFullTextQuery(const QString &fullTextQuery) {
  if (fullTextQuery_ == fullTextQuery)
    return;
  fullTextQuery_ = fullTextQuery;
  Q_EMIT fullTextQueryChanged(fullTextQuery_);
}

void ViewModel::setSortProperty(const QString &sortProperty) {
  if (sortProperty_ == sortProperty)
    return;
  sortProperty_ = sortProperty;
  Q_EMIT sortPropertyChanged(sortProperty_);
}

void ViewModel::setSortOrderAsc(bool sortOrderAsc) {
  if (sortOrderAsc_ == sortOrderAsc)
    return;
  sortOrderAsc_ = sortOrderAsc;
  Q_EMIT sortOrderAscChanged(sortOrderAsc_);
}

void ViewModel::setCaseInsensive(bool caseInsensive) {
  if (caseInsensive_ == caseInsensive)
    return;
  caseInsensive_ = caseInsensive;
  Q_EMIT caseInsensiveChanged(caseInsensive_);
}

void ViewModel::setSearchFullPath(bool searchFullPath) {
  if (searchFullPath_ == searchFullPath)
    return;
  searchFullPath_ = searchFullPath;
  Q_EMIT searchFullPathChanged(searchFullPath_);
}

void ViewModel::setEnableCompression(bool enableCompression) {
  if (enableCompression == enableCompression_)
    return;
  enableCompression_ = enableCompression;
  Q_EMIT enableCompressionChanged(enableCompression_);
}

// bool ViewModel::openManual() {
//   auto state = manual_->openManual();
//   switch (state) {
//   case ManualError::NoError:
//     return true;
//   case ManualError::CopyFileError:
//     setMessage("拷贝用户手册失败!");
//     return false;
//   case ManualError::OpenFileError:
//     setMessage("打开用户手册失败!");
//     return false;
//   case ManualError::NotExistsError:
//     setMessage("找不到用户手册!");
//     return false;
//   }
//   return false;
// }

int ViewModel::elapsedSecondsSinceIndexUpdated() {
  auto now = std::chrono::steady_clock::now();
  if (searchEngineService_->indexLastUpdatedTime() ==
      std::chrono::steady_clock::time_point()) {
    return 0;
  }
  return std::chrono::duration_cast<std::chrono::seconds>(
             now.time_since_epoch() -
             searchEngineService_->indexLastUpdatedTime().time_since_epoch())
      .count();
}

void ViewModel::endBackgroundIndexFileContentFiltering() {
  if (filterState() == FilterState::Processing){
    setFilterState(FilterState::Canceling);
    endBackgroundIndexFileContentTask();
  }
}

void ViewModel::setMessage(const QString &message) {
  if (message_ == message)
    return;
  message_ = message;
  Q_EMIT messageChanged(message_);
}

std::optional<TermsQueryMatcher<AutoMpmEngine>>
ViewModel::buildFullTextTermsMatcher() const {
  if (parsedFullTextQuery_) {
    auto termsQuery =
        dynamic_cast<const TermsQuery *>(parsedFullTextQuery_.get());
    if (termsQuery) {
      TermsQueryMatchEngine<AutoMpmEngine> engine(
          termsQuery, true /* 全文匹配总是不区分大小写的 */);
      return std::make_optional<TermsQueryMatcher<AutoMpmEngine>>(
          engine.matcher());
    }
  }
  return std::optional<TermsQueryMatcher<AutoMpmEngine>>();
}

std::optional<TermsQueryMatcher<AutoMpmEngine>>
ViewModel::buildFileNameTermsMatcher(bool caseInsensive) const {
  if (parsedFileNameQuery_) {
    auto termsQuery =
        dynamic_cast<const TermsQuery *>(parsedFileNameQuery_.get());
    if (termsQuery) {
      TermsQueryMatchEngine<AutoMpmEngine> engine(
          termsQuery, caseInsensive);
      return std::make_optional<TermsQueryMatcher<AutoMpmEngine>>(
          engine.matcher());
    }
  }
  return std::optional<TermsQueryMatcher<AutoMpmEngine>>();
}

void ViewModel::setActiveDocId(unsigned int docId) {
  if (activeDocId_ == docId)
    return;
  activeDocId_ = docId;
  Q_EMIT activeDocIdChanged(activeDocId_);
}

void ViewModel::setTopHits(std::unique_ptr<QTopHits> m) {
  // 注意这里不要使用 topHits_ = std::move(m), 这回首先使 topHits_ 里边原有的
  // QObject 销毁, QObject 销毁过程中会将 qml 中绑定的属性设置为 null.
  // 所以我们改一下顺序, 通过使用 swap 使得我们先改变值，然后发送信号,
  // 最后再销毁原有的 QObject
  std::swap(topHits_, m);
  Q_EMIT topHitsChanged(topHits_.get());
}

void ViewModel::setPendingFilesSet(std::unique_ptr<QDocSet> m) {
  // 注意这里不要使用 pendingFilesSet_ = std::move(m), 这回首先使
  // pendingFilesSet_ 里边原有的 QObject 销毁, QObject 销毁过程中会将 qml
  // 中绑定的属性设置为 null. 所以我们改一下顺序, 通过使用 swap
  // 使得我们先改变值，然后发送信号, 最后再销毁原有的 QObject
  std::swap(pendingFilesSet_, m);
  Q_EMIT pendingFilesSetChanged(pendingFilesSet_.get());
}


// void ViewModel::setDeletedFileCount(int count) {
//   std::lock_guard<std::mutex> lock(deletedFileCountMutex_);
//   if (deletedFileCount_ == count)
//     return;
//     deletedFileCount_ = count;
//   Q_EMIT deletedFileCountChanged(count);
// }

void ViewModel::onIndexCommited(FileContentIndexService::OpStamp opstamp) {
  // 更新最新的 fullText 索引时间戳, 这会触发下次搜索的时候先重新加载索引
  fullTextIndexOutdated_ = true;
}
bool ViewModel::needToParse() {
  if (fullTextQuery_.isEmpty() ) return false;
  if(topHits_ && topHits_->size() >= configService_->maxHits()) {
    return false;
  }
  return true;
}
void ViewModel::onDocIndexed(
    DocId id, const std::filesystem::path &path, const std::u8string &text,
    std::optional<core::FileContentIndexService::OpStamp> opstamp, std::optional<SearchEngineService::Document> doc,
    void *context) {
  // SPDLOG_DEBUG(fmt::format("start onDocIndexed: {}, {}", id, reinterpret_cast<const char *>(path.u8string().c_str())));
  // 如果不是我们自己发出的请求, 直接忽略
  if (context != this)
    return;
  bool deletedFile = doc.has_value() ? true : false;
  if (!deletedFile && pendingFilesSet_ && pendingFilesSet_->size() > id) {
    SPDLOG_DEBUG(
        fmt::format("done onDocIndexed: {}, {}, {}", id,
                    reinterpret_cast<const char *>(path.u8string().c_str()),
                    pendingFilesSet_->count()));
    pendingFilesSet_->remove(id);
  }

  // 当前的搜索结果展示数量已经到达上限, 跳过后续所有操作
  if(topHits_ && topHits_->size() >= configService_->maxHits()) {
    // if (deletedFile && deletedFilesTable_) deletedFilesTable_->clearTable();
    return;
  }
  // 启动UI更新定时器（如果尚未启动）
  if (!uiUpdateTimer_->isActive()) {
      uiUpdateTimer_->start();
  }
  
  if (deletedFile && fileNameTermsMatcher_ && !fileNameTermsMatcher_->match(path.filename().u8string().c_str())) {
    return;
  }
  // 我们新索引了一个文件,
  // 那么我们需要判断当前文件是否需要增加到当前界面上显示的搜索结果中，
  // 也就是我们手动做一次内容匹配，匹配上了就添加到当前的搜索结果中。
  // 注意这里我们只判断了 fullText
  // 的条件，并没有重新匹配文件名等搜索条件，因为我们隐式的认为,
  // 在搜索过程中发出的 onDocIndexed 消息一定是命中了文件名等条件后才索引的
  if ((fullTextTermsMatcher_ && fullTextTermsMatcher_->match(text.c_str())) || deletedFile) {
    if (!doc.has_value())
      doc = searchEngineService_->getDocumentSync(id);
    if (doc) {
      auto fileDirHighlighted = doc->name().parent_path().u8string();
      auto fileNameHighlighted = doc->name().filename().u8string();
      if (filePathHighlighter_.has_value()) {
        /* TODO)) 不包含目录名称时, 要不要高亮文件名 */
        fileDirHighlighted =
            filePathHighlighter_.value()
                .highlightAll(doc->name().parent_path().u8string())
                .text();
        fileNameHighlighted =
            filePathHighlighter_.value()
                .highlightAll(doc->name().filename().u8string())
                .text();
      }

      std::u8string fullTextHighlighted = u8"";
      auto path = doc->name();
      auto name = path.filename();
      auto dir = path.parent_path();
      
      if (fullTextHighlighter_.has_value()) {
        TextNormalizer textNormalizer;
        auto beautifyText = textNormalizer.normalize(text);
        fullTextHighlighted =
            fullTextHighlighter_.value().highlightBest(beautifyText).text();
      } /*else {
        fullTextHighlighted = beautifyText;
      }*/
      // 使用优化后的构造函数 - 移除了未使用的字段
      auto hit = std::make_unique<search::Hit>(
          doc->id(), path, name, dir, doc->attributes(), doc->size(),
          doc->creationTime(), doc->lastModificationTime(),
          fileDirHighlighted, fileNameHighlighted, fullTextHighlighted);

      if (topHits_) {
        // 使用批量添加而不是立即添加，减少UI更新频率
        addHitToBatch(std::move(*hit.get()));
      }
    }
  }
}
// @@@@liukai 搜索文件的入口
void ViewModel::search(const QString &fileNameQuery,
                       const QString &fullTextQuery,
                       const QString &sortProperty, bool sortOrderAsc,
                       bool caseInsensive, bool searchFullPath) {
  if (indexState() != IndexState::Success)
    return;
  startSearchTask(fileNameQuery, fullTextQuery, sortProperty, sortOrderAsc,
                  caseInsensive, searchFullPath);
}

// 带筛选条件的搜索方法
void ViewModel::searchWithFilters(const QString &fileNameQuery,
                                  const QString &fullTextQuery,
                                  const QString &sortProperty, bool sortOrderAsc,
                                  bool caseInsensive, bool searchFullPath,
                                  const QString &createTimeFrom, const QString &createTimeTo,
                                  const QString &modifyTimeFrom, const QString &modifyTimeTo,
                                  const QString &fileSizeMin, const QString &fileSizeMax) {
  if (indexState() != IndexState::Success)
    return;

  // 目前先调用原有的搜索方法，后续可以扩展支持时间和大小筛选
  // TODO: 实现时间和文件大小筛选逻辑
  startSearchTask(fileNameQuery, fullTextQuery, sortProperty, sortOrderAsc,
                  caseInsensive, searchFullPath);

  // 记录筛选条件用于后续处理
  if (!createTimeFrom.isEmpty() || !createTimeTo.isEmpty() ||
      !modifyTimeFrom.isEmpty() || !modifyTimeTo.isEmpty() ||
      !fileSizeMin.isEmpty() || !fileSizeMax.isEmpty()) {
    SPDLOG_INFO("Advanced filters applied: createTime[{}-{}], modifyTime[{}-{}], fileSize[{}-{}]",
                createTimeFrom.toStdString(), createTimeTo.toStdString(),
                modifyTimeFrom.toStdString(), modifyTimeTo.toStdString(),
                fileSizeMin.toStdString(), fileSizeMax.toStdString());
  }
}

void ViewModel::searchDeletedFiles(const QString &fileNameQuery,
  const QString &fullTextQuery,
  const QString &sortProperty, bool sortOrderAsc,
  bool caseInsensive, bool searchFullPath) {
    // if (indexState() != IndexState::Success)
    //     return;
    startSearchDeletedFilesTask(fileNameQuery, fullTextQuery, sortProperty, sortOrderAsc,
                                caseInsensive, searchFullPath);
}

boost::future<void> ViewModel::doSearch(
    const QString &fileNameQueryStr, const QString &fullTextQueryStr,
    const QByteArray &sortPropertyUtf8, bool sortOrderAsc, bool caseInsensive,
    bool searchFullPath, const CancellationToken &cancellationToken) {
  return fileContentIndexService_->commit()
      .then(*mainThreadExecutor_,
            [this, cancellationToken = cancellationToken](auto &&f) {
              if (cancellationToken.isCancellationRequested())
                throw std::runtime_error("cancelled");
              return reloadFullTextIndex();
            })
      .unwrap()
      .then(
          *mainThreadExecutor_,
          [this, fileNameQueryStr, fullTextQueryStr, sortPropertyUtf8, sortOrderAsc,
           caseInsensive, searchFullPath,
           cancellationToken = cancellationToken](boost::future<void> &&_) {
            if (cancellationToken.isCancellationRequested())
              throw std::runtime_error("cancelled");

            // 使用预转换的UTF-8字符串，避免重复转换
            Sort sort(sortPropertyUtf8.constData(),
                      sortOrderAsc ? Sort::Direction::ASC
                                   : Sort::Direction::DESC);

            SearchEngineService::SearchOptions searchOptions;
            searchOptions.caseInsensive = caseInsensive;
            searchOptions.searchFullPath = searchFullPath;
            searchOptions.includeArchive = enableCompression();

            return searchEngineService_
                ->search2(parsedFileNameQuery_ ? parsedFileNameQuery_->clone()
                                               : nullptr,
                          parsedFullTextQuery_ ? parsedFullTextQuery_->clone()
                                               : nullptr,
                          sort, searchOptions)
                .then(
                    *mainThreadExecutor_,
                    [this, searchOptions,
                     cancellationToken = cancellationToken](
                        boost::future<std::pair<SearchEngineService::TopHits,
                                                DocSet>> &&f) mutable {
                      if (cancellationToken.isCancellationRequested())
                        throw std::runtime_error("cancelled");

                      auto v = f.get();
                      auto &hits = v.first;
                      auto &docSet = v.second;
                      // @@@@liukai    get the search result here!

                      // 如果没有全文搜索查询且结果超过5000条，使用分批次添加来提升界面响应速度
                      if (!parsedFullTextQuery_ && hits.size() > 10000) {
                        // 创建空的QTopHits对象
                        setTopHits(std::make_unique<QTopHits>(search::TopHits(), fileIconProvider_));

                        // 在IO线程中启动分批次添加任务，避免阻塞界面
                        startBatchAddHitsTask(std::move(hits), cancellationToken, searchOptions, docSet);
                      } else {
                        // 有全文搜索时或结果不超过5000条时，保持原有的一次性设置方式
                        setTopHits(std::make_unique<QTopHits>(std::move(hits), fileIconProvider_));
                      }

                      // 只有用户进行了全文内容检索时,
                      // 我们才开始对文件全文做索引
                      if (dynamic_cast<const TermsQuery *>(
                              parsedFullTextQuery_.get())) {
                        startBackgroundIndexFileContentTask(
                            docSet, searchOptions.includeArchive);
                      } else {
                        setFilterState(FilterState::Idle);
                        setPendingFilesSet(std::make_unique<QDocSet>(DocSet()));
                      }

                      return;
                    });
          })
      .unwrap()
      .then(*mainThreadExecutor_, [this, cancellationToken = cancellationToken](
                                      boost::future<void> &&f) {
        if (cancellationToken.isCancellationRequested())
          throw std::runtime_error("cancelled");
        setSearching(false);
        // 搜索结束时停止定时器并执行最终的UI更新
        stopUIUpdateTimer();
        endSearchTime();
        return f.get();
      });
}



boost::future<void> ViewModel::doSearchDeletedFiles(
  const QString &fileNameQueryStr, const QString &fullTextQueryStr,
  const QString &sortProperty, bool sortOrderAsc, bool caseInsensive,
  bool searchFullPath, const CancellationToken &cancellationToken) {
    // deletedFilesTable_.reset();
    // if (db_delete_files_ != nullptr) delete db_delete_files_;
    // QString tmpDBPath_ =
    // QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) +
    // "/tmp/";
    // QString dbname =  "delfiles.db";
    // std::filesystem::path filepath_path((tmpDBPath_ + dbname).toStdString());
    // try {
    //   std::filesystem::remove(filepath_path);
    // } catch (...) {
    // }
    // db_delete_files_ = new Database((tmpDBPath_ + dbname).toStdString());
    // deletedFilesTable_.reset(new DeletedFilesTable(*db_delete_files_));
    // deletedFilesTable_->clearTable();
    // searchEngineService_->setDeleteFileTable(deletedFilesTable_);
  auto includeArchive = enableCompression();
  setIndexState(IndexState::Loading);
  return boost::make_ready_future()
      .then(*ioTheadExecutor_,
       [this, includeArchive,
        cancellationToken](boost::future<void> &&f) {
         if (cancellationToken.isCancellationRequested())
           throw std::runtime_error("cancelled");

         // 只等待deletedFilesTable_创建，不等待后台删除文件收集完成
         waitForDeletedFilesTableCreation(cancellationToken);
         searchEngineService_->setDeleteFileTable(deletedFilesTable_);

         if (cancellationToken.isCancellationRequested())
           throw std::runtime_error("cancelled");
         }

  ).then(*mainThreadExecutor_, [this, includeArchive,
        cancellationToken](boost::future<void> &&f){
          // UI状态更新必须在主线程中进行
          if (cancellationToken.isCancellationRequested())
           throw std::runtime_error("cancelled");
          // setDeletedFileCount(deletedFilesTable_->getRecordCount());
          setIndexState(IndexState::Success);
           IndexDeletedFileContentTask indexDeletedFileContentTask(
               configService_, searchEngineService_,
               fileContentIndexService_, mainThreadExecutor_, ioTheadExecutor_, [this](const std::filesystem::path &filename){
                this->clean(filename);
               }, deletedFileCollectionService_);
           return indexDeletedFileContentTask(
               deletedFilesTable_, includeArchive, this,  [this](){return this->needToParse();},
               cancellationToken,
               [this]() -> bool {
                 // 检查是否应该继续处理：如果已达到最大结果数则停止
                 return !topHits_ || topHits_->size() < configService_->maxHits();
               },
               [](auto docId) {});
        })
      .unwrap().then(*mainThreadExecutor_, [this, cancellationToken = cancellationToken](
        boost::future<void> &&f) {
        // 所有UI状态更新必须在主线程中进行
        setIndexState(IndexState::Success);
        setFilterState(FilterState::Idle);
        setSearching(false);
        // 搜索结束时停止定时器并执行最终的UI更新
        stopUIUpdateTimer();
        endSearchTime();
        return f.get();
        });
}


void ViewModel::startSearchDeletedFilesTask(const QString &fileNameQuery,
  const QString &fullTextQuery,
  const QString &sortProperty, bool sortOrderAsc,
  bool caseInsensive, bool searchFullPath) {
    if (searching())
    return;

  // 取消之前的批量添加任务
  cancelBatchAddTask_ = true;

  setSearching(true);
  setFilterState(FilterState::Processing);
  startSearchTime();
  
  // 保存搜索历史
  addFileNameHistory(fileNameQuery);
  addFullTextHistory(fullTextQuery);
  // setDeletedFileCount(0);

  // 清空待处理的UI更新缓冲区，准备新的搜索
  {
    std::lock_guard<std::mutex> lock(uiUpdateMutex_);
    pendingHits_.clear();
  }
  lastEmittedDeletedFileCount_ = -1;

  // 启动UI更新定时器
  uiUpdateTimer_->start();

  // 预先转换为UTF-8，避免在lambda中重复转换
  QByteArray fileNameQueryUtf8 = fileNameQuery.toUtf8();
  QByteArray fullTextQueryUtf8 = fullTextQuery.toUtf8();
  QByteArray sortPropertyUtf8 = sortProperty.toUtf8();

  backgroundIndexFileContentTaskCancallationSource_ = CancellationSource();
  searchTaskFuture_ = backgroundIndexFileContentTaskFuture_.then(*mainThreadExecutor_,
    [](auto &&) {})
          .then(*mainThreadExecutor_,
                [this,
                 cancellationToken = backgroundIndexFileContentTaskCancallationSource_.getToken(),
                 fileNameQuery, fullTextQuery, sortProperty, sortOrderAsc,
                 caseInsensive, searchFullPath,
                 fileNameQueryUtf8 = std::move(fileNameQueryUtf8),
                 fullTextQueryUtf8 = std::move(fullTextQueryUtf8),
                 sortPropertyUtf8 = std::move(sortPropertyUtf8)](boost::future<void> &&f) mutable {
                  // if (cancellationToken.isCancellationRequested())
                  //   throw std::runtime_error("cancelled");
                  setTopHits(std::make_unique<QTopHits>(search::TopHits(),
                                                        fileIconProvider_));
                  setFileNameQuery(fileNameQuery);
                  setFullTextQuery(fullTextQuery);
                  setSortProperty(sortProperty);
                  setSortOrderAsc(sortOrderAsc);
                  setCaseInsensive(caseInsensive);
                  setSearchFullPath(searchFullPath);

                  // 使用预转换的UTF-8字符串，避免重复转换
                  parsedFileNameQuery_ = !fileNameQueryUtf8.isEmpty() ?
                      queryParser_->parse(reinterpret_cast<const char8_t *>(
                          fileNameQueryUtf8.constData())) : nullptr;
                  filePathHighlighter_ =
                      parsedFileNameQuery_
                          ? std::make_optional<Highlighter>(
                                highlighterFactory_->create(
                                    parsedFileNameQuery_.get(), caseInsensive_))
                          : std::optional<Highlighter>();
                  parsedFullTextQuery_ = !fullTextQueryUtf8.isEmpty() ?
                      queryParser_->parse(reinterpret_cast<const char8_t *>(
                          fullTextQueryUtf8.constData())) : nullptr;
                  fullTextHighlighter_ =
                      parsedFullTextQuery_
                          ? std::make_optional<Highlighter>(
                                highlighterFactory_->create(
                                    parsedFullTextQuery_.get(),
                                    true /* 全文匹配总是不区分大小写的 */))
                          : std::optional<Highlighter>();
                  fileNameTermsMatcher_ = buildFileNameTermsMatcher(caseInsensive);
                  fullTextTermsMatcher_ = buildFullTextTermsMatcher();
                  return doSearchDeletedFiles(fileNameQuery, fullTextQuery, sortProperty,
                                  sortOrderAsc, caseInsensive, searchFullPath,
                                  cancellationToken);
                })
          .unwrap()
          .share();
}

void ViewModel::startSearchTask(const QString &fileNameQuery,
                                const QString &fullTextQuery,
                                const QString &sortProperty, bool sortOrderAsc,
                                bool caseInsensive, bool searchFullPath) {
  if (searching())
    return;

  // 取消之前的批量添加任务
  cancelBatchAddTask_ = true;

  setSearching(true);
  setFilterState(FilterState::Processing);
  startSearchTime();

  // 保存搜索历史
  addFileNameHistory(fileNameQuery);
  addFullTextHistory(fullTextQuery);

  // 预先转换为UTF-8，避免在lambda中重复转换
  QByteArray fileNameQueryUtf8 = fileNameQuery.toUtf8();
  QByteArray fullTextQueryUtf8 = fullTextQuery.toUtf8();
  QByteArray sortPropertyUtf8 = sortProperty.toUtf8();

  searchTaskCancallationSource_ = CancellationSource();
  searchTaskFuture_ =
      endBackgroundIndexFileContentTask()
          .then(*mainThreadExecutor_,
                [this,
                 cancellationToken = searchTaskCancallationSource_.getToken(),
                 fileNameQuery, fullTextQuery, sortProperty, sortOrderAsc,
                 caseInsensive, searchFullPath,
                 fileNameQueryUtf8 = std::move(fileNameQueryUtf8),
                 fullTextQueryUtf8 = std::move(fullTextQueryUtf8),
                 sortPropertyUtf8 = std::move(sortPropertyUtf8)](boost::future<void> &&f) mutable {
                  if (cancellationToken.isCancellationRequested())
                    throw std::runtime_error("cancelled");

                  setFileNameQuery(fileNameQuery);
                  setFullTextQuery(fullTextQuery);
                  setSortProperty(sortProperty);
                  setSortOrderAsc(sortOrderAsc);
                  setCaseInsensive(caseInsensive);
                  setSearchFullPath(searchFullPath);

                  // 使用预转换的UTF-8字符串，避免重复转换
                  parsedFileNameQuery_ = !fileNameQueryUtf8.isEmpty() ?
                      queryParser_->parse(reinterpret_cast<const char8_t *>(
                          fileNameQueryUtf8.constData())) : nullptr;
                  filePathHighlighter_ =
                      parsedFileNameQuery_
                          ? std::make_optional<Highlighter>(
                                highlighterFactory_->create(
                                    parsedFileNameQuery_.get(), caseInsensive_))
                          : std::optional<Highlighter>();
                  parsedFullTextQuery_ = !fullTextQueryUtf8.isEmpty() ?
                      queryParser_->parse(reinterpret_cast<const char8_t *>(
                          fullTextQueryUtf8.constData())) : nullptr;
                  fullTextHighlighter_ =
                      parsedFullTextQuery_
                          ? std::make_optional<Highlighter>(
                                highlighterFactory_->create(
                                    parsedFullTextQuery_.get(),
                                    true /* 全文匹配总是不区分大小写的 */))
                          : std::optional<Highlighter>();
                  fullTextTermsMatcher_ = buildFullTextTermsMatcher();
                  fileNameTermsMatcher_ = buildFileNameTermsMatcher(caseInsensive);
                  return doSearch(fileNameQuery, fullTextQuery, sortPropertyUtf8,
                                  sortOrderAsc, caseInsensive, searchFullPath,
                                  cancellationToken);
                })
          .unwrap()
          .share();
}

boost::future<void> ViewModel::endSearchTask() {
  searchTaskCancallationSource_.requestCancellation();
  return searchTaskFuture_.then(*mainThreadExecutor_, [](auto &&) {});
}

/**
 * @brief 开始后台内容索引
 *
 * @param docSet 待索引的文档集合
 */
void ViewModel::startBackgroundIndexFileContentTask(const DocSet &docSet,
                                                    bool includeArchive) {
  backgroundIndexFileContentTaskCancallationSource_ = CancellationSource();
  auto cancellationToken =
      backgroundIndexFileContentTaskCancallationSource_.getToken();

  backgroundIndexFileContentTaskFuture_ =
      filterNotIndexedDocSet(docSet, cancellationToken)
          .then(*mainThreadExecutor_,
                [this, includeArchive,
                 cancellationToken](boost::future<DocSet> &&f) {
                  if (cancellationToken.isCancellationRequested())
                    throw std::runtime_error("cancelled");
                  auto docSet = f.get();
                  setPendingFilesSet(std::make_unique<QDocSet>(docSet));
                  IndexFileContentTask indexFileContentTask(
                      configService_, searchEngineService_,
                      fileContentIndexService_, mainThreadExecutor_, ioTheadExecutor_);

                  return indexFileContentTask(docSet, includeArchive, [this](){return this->needToParse();},
                                              this,
                                              cancellationToken,
                                              [this]() -> bool {
                                                // 检查是否应该继续处理：如果已达到最大结果数则停止
                                                return !topHits_ || topHits_->size() < configService_->maxHits();
                                              },
                                              [](auto docId) {});
                })
          .unwrap().then(*mainThreadExecutor_, [this](boost::future<void> &&f) {
            setFilterState(FilterState::Idle);
            auto path = searchEngineService_->fullTextIndexWriter().rootPath();
            if (path != std::filesystem::path()) {
              std::filesystem::remove_all(path);
            }
          })
          .share();
}

boost::future<void> ViewModel::endBackgroundIndexFileContentTask() {
  backgroundIndexFileContentTaskCancallationSource_.requestCancellation();
  stopUIUpdateTimer();
  return backgroundIndexFileContentTaskFuture_.then(*mainThreadExecutor_,
                                                    [](auto &&) {});
}

void ViewModel::onSearchEngineStateChanged(
    core::SearchEngineService::State state) {
  switch (state) {
  case core::SearchEngineService::State::Init:
    setIndexState(IndexState::Init);
    break;
  case core::SearchEngineService::State::Loading:
    setIndexState(IndexState::Loading);
    break;
  case core::SearchEngineService::State::Success:
    setIndexState(IndexState::Success);
    setIndexedFilesCnt(
        static_cast<unsigned int>(searchEngineService_->filesCnt()));
    setRoots(searchEngineService_->roots());
    // @@@@ liukai not to search when init
    //search("", "", "", false, false, false);
    break;
  case core::SearchEngineService::State::Error:
    setIndexState(IndexState::Error);
    if (searchEngineService_->error()) {
      try {
        std::rethrow_exception(searchEngineService_->error());
      } catch (std::exception &e) {
        setIndexErrorReason(QString::fromUtf8(e.what()));
      } catch (...) {
        setIndexErrorReason(QString::fromUtf8("unknown error"));
      }
    }
    break;
  };
}

void ViewModel::onLoadIndexProgressChanged(float progress) {
  setIndexProgress(progress);
}

boost::future<void> ViewModel::reloadFullTextIndex() {
  if (fullTextIndexOutdated_) {
    fullTextIndexOutdated_ = false;
    return reloadFullTextIndex();
  } else {
    return boost::make_ready_future();
  }
}

boost::future<DocSet>
ViewModel::filterNotIndexedDocSet(DocSet docSet,
                                  const CancellationToken &cancellationToken) {
  // 我们不过滤文件了, 所有文件都支持提取内容
  // auto formats = configService_->enabledFileFormats();

  // std::vector<std::u8string> extensions;
  // if (formats.contains(FileFormat::DOC)) {
  //   extensions.push_back(u8".doc");
  //   extensions.push_back(u8".wps");
  // }
  // if (formats.contains(FileFormat::DOCX)) {
  //   extensions.push_back(u8".docx");
  // }
  // if (formats.contains(FileFormat::XLS)) {
  //   extensions.push_back(u8".xls");
  //   extensions.push_back(u8".et");
  // }
  // if (formats.contains(FileFormat::XLSX)) {
  //   extensions.push_back(u8".xlsx");
  // }
  // if (formats.contains(FileFormat::PPT)) {
  //   extensions.push_back(u8".ppt");
  //   extensions.push_back(u8".dps");
  // }
  // if (formats.contains(FileFormat::PPTX)) {
  //   extensions.push_back(u8".pptx");
  // }
  // if (formats.contains(FileFormat::PDF)) {
  //   extensions.push_back(u8".pdf");
  // }
  // if (formats.contains(FileFormat::ZIP)) {
  //   extensions.push_back(u8".zip");
  // }
  // if (formats.contains(FileFormat::RAR)) {
  //   extensions.push_back(u8".rar");
  // }
  // if (formats.contains(FileFormat::Z7)) {
  //   extensions.push_back(u8".7z");
  // }
  // if (formats.contains(FileFormat::TAR)) {
  //   extensions.push_back(u8".tar");
  // }

  // return searchEngineService_->filterDocSetByFileExtensions(
  //     std::move(docSet), cancellationToken, extensions);

  return boost::make_ready_future(std::move(docSet));
}



void ViewModel::waitForCollectDeletedFilesCompletion(const CancellationToken& cancellationToken) {
  if (!deletedFileCollectionService_) {
    return;
  }

  // 使用服务的支持取消的等待方法
  deletedFilesTable_ = deletedFileCollectionService_->waitForCompletion(cancellationToken);
}

void ViewModel::waitForDeletedFilesTableCreation(const CancellationToken& cancellationToken) {
  if (!deletedFileCollectionService_) {
    return;
  }

  // 只等待deletedFilesTable_创建，不等待后台收集完成
  // 使用轮询方式等待表创建
  const auto pollInterval = std::chrono::milliseconds(100);

  while (!deletedFilesTable_) {
    // 检查是否被取消
    if (cancellationToken.isCancellationRequested()) {
      throw std::runtime_error("cancelled");
    }

    // 尝试获取已创建的表（即使收集未完成）
    deletedFilesTable_ = deletedFileCollectionService_->getDeletedFilesTable();

    if (!deletedFilesTable_) {
      // 短暂等待
      std::this_thread::sleep_for(pollInterval);
    }
  }
}

// 统一的UI更新实现
void ViewModel::performUIUpdate() {
  std::vector<search::Hit> hitsToAdd;
  int currentDeletedCount = 0;
  bool hasUpdates = false;

  // 获取待更新的数据
  {
    std::lock_guard<std::mutex> lock(uiUpdateMutex_);
    if (!pendingHits_.empty()) {
      hitsToAdd = std::move(pendingHits_);
      pendingHits_.clear();
      hasUpdates = true;
    }
  }

  // 执行UI更新（在锁外执行，避免阻塞）
  if (topHits_ && !hitsToAdd.empty()) {
    topHits_->addHitsBatch(std::move(hitsToAdd));
  }

  if (currentDeletedCount != lastEmittedDeletedFileCount_) {
    lastEmittedDeletedFileCount_ = currentDeletedCount;
    Q_EMIT deletedFileCountChanged(currentDeletedCount);
    hasUpdates = true;
  }

  // 优化：如果没有搜索活动且没有待更新数据，停止定时器
  if (!searching_ && !hasUpdates) {
    if (uiUpdateTimer_->isActive()) {
      uiUpdateTimer_->stop();
      SPDLOG_DEBUG("UI update timer stopped - no active search or pending updates");
    }
  }
}

void ViewModel::addHitToBatch(search::Hit &&hit) {
  {
    std::lock_guard<std::mutex> lock(uiUpdateMutex_);
    pendingHits_.push_back(std::move(hit));
  }

  // 优化：完全依赖定时器更新，避免频繁的立即更新
  // 只在搜索期间且定时器未运行时启动定时器
  if (searching_ && !uiUpdateTimer_->isActive()) {
    uiUpdateTimer_->start();
    SPDLOG_DEBUG("UI update timer started for search");
  }
}

// 优化：统一的定时器停止方法
void ViewModel::stopUIUpdateTimer() {
  if (uiUpdateTimer_->isActive()) {
    uiUpdateTimer_->stop();
    SPDLOG_DEBUG("UI update timer stopped");
  }

  // 执行最终的UI更新，确保所有待处理数据都被处理
  performUIUpdate();
}

void ViewModel::startBatchAddHitsTask(search::TopHits &&hits, const CancellationToken &cancellationToken,
                                      const SearchEngineService::SearchOptions &searchOptions, const DocSet &docSet) {
  // 重置取消标志
  cancelBatchAddTask_ = false;

  // 分批次大小，可以根据需要调整
  const size_t batchSize = 10000;
  const size_t totalHits = hits.size();

  SPDLOG_DEBUG("Starting batch add hits task with {} hits, batch size: {}", totalHits, batchSize);

  // 在IO线程中执行批次添加任务，完全避免阻塞界面
  ioTheadExecutor_->submit([this, hits = std::move(hits), cancellationToken, searchOptions, docSet, batchSize, totalHits]() mutable {
    try {
      for (size_t i = 0; i < totalHits; i += batchSize) {
        // 检查是否需要取消
        if (cancelBatchAddTask_ || cancellationToken.isCancellationRequested()) {
          return;
        }

        size_t endIdx = std::min(i + batchSize, totalHits);
        std::vector<search::Hit> batch;
        batch.reserve(endIdx - i);

        // 从hits中移动数据到批次
        for (size_t j = i; j < endIdx; ++j) {
          batch.push_back(std::move(const_cast<search::Hit&>(hits[j])));
        }

        // 在主线程中添加批次到UI
        mainThreadExecutor_->submit([this, batch = std::move(batch), cancellationToken]() mutable {
          if (topHits_ && !cancelBatchAddTask_ && !cancellationToken.isCancellationRequested()) {
            topHits_->addHitsBatch(std::move(batch));
          }
        });

        // 添加延迟，让界面有时间响应
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
      }
    } catch (...) {
    }
  });
}

} // namespace app
} // namespace anywhere
