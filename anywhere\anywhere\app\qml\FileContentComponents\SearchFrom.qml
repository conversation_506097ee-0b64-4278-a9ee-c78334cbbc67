import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Layouts 1.15
import SkyUi 1.0
import AnywhereQmlModule 1.0

SkyCard {
  property bool expanded: false
  property bool previewerEnabled: false
  property bool caseInsensive: true
  property bool searchFullPath: false
  property bool enableCompression: false
  property string name: ""
  property string fileNameQuery: ""
  property string fullTextQuery: ""
  property var filenamesHistory: []
  property var fullTextsHistory: []

  // 新增筛选条件属性
  property string createTimeFrom: ""
  property string createTimeTo: ""
  property string modifyTimeFrom: ""
  property string modifyTimeTo: ""
  property string fileSizeMin: ""
  property string fileSizeMax: ""
  property bool showAdvancedFilters: false

  anchors.fill: parent
  signal formChage(string fileName, string fullText, bool caseInsensive, bool searchOnlyFileName,
                   string createTimeFrom, string createTimeTo, string modifyTimeFrom, string modifyTimeTo,
                   string fileSizeMin, string fileSizeMax)
  signal setEnableCompression(bool enableCompression)
  signal previewShouldCollapse()  // 新增：通知需要折叠preview的信号

  backgroundValue: "transparent"
  paddingValue: 0
  marginValue: 0
  radiusValue: 0
  id: root

  onFileNameQueryChanged: {
    if (fileName.fileNameQuery == fileNameQuery) return;
    fileName.value = fileNameQuery;
  }

  content: Item{
    anchors.fill: parent
    
    RowLayout {
      id: fileNameRow
      x: {
        return 0
      }
      y: {
        return 0
      }
      width: {
        if (previewerEnabled) {
          return parent.width - searchButton.width - 20
        } else {
          return (parent.width - searchButton.width) / 2 - 20
        }
      }
      height: {
        if (previewerEnabled) {
          return 30
        } else {
          return parent.height
        }
      }
      SkyTitle {
        text: qsTr("文件名: ")
        Layout.preferredWidth: 80
        Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
        Layout.fillHeight: true
        description: qsTr("多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font><br>可以在关键词前加<font color=\"red\">+</font>或<font color=\"red\">-</font>表示必须包含或者不得包含关键词，如：<font color=\"red\">财务 绩效 考核 +内部文件</font><br>表示结果中<font color=\"red\">包含财务或绩效或考核，同时必须包含内部文件</font>")
      }
      SkyInpuSelect {
        id: fileName
        Layout.fillWidth: true
        implicitHeight: parent.height
        placeholderText: "在此输入文件名搜索条件"
        contentDescription: "多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font>"
        prefixOptions: {
          return viewModel.roots
        }
        prefixDescription: {
          return "当前可搜索" + viewModel.roots.join(",") + "盘上的文件"
        }
        skyRightMeun: Component {
          id: fileNameRightMeun
          RowLayout {
            width: 50
            height: 25
            spacing: 0
            Item {
              Layout.fillWidth: true
              Layout.fillHeight: true
              
              SkyButton {
                anchors.fill: parent
                iconSize: skyTheme.fontSize
                type: "text"
                shape: "circle"
                enabledHovered: false
                iconSource: SkyIcons.Font
                showDisabledIcon: root.caseInsensive
                contentDescription: {
                  if (root.caseInsensive) {
                    return "未开启区分大小写"
                  } else {
                    return "已开启区分大小写"
                  }
                }
                skyIconColor: {
                  if (root.caseInsensive)
                    return "#333"
                  else
                    return "#1890FF"
                }
                onClicked: {
                  root.caseInsensive = !root.caseInsensive
                }
              }
            }
            Item {
              Layout.fillWidth: true
              Layout.fillHeight: true
              
              SkyButton {
                anchors.fill: parent
                iconSize: skyTheme.fontSize
                type: "text"
                shape: "circle"
                enabledHovered: false
                iconSource: SkyIcons.OpenFolderHorizontal
                showDisabledIcon: !root.searchFullPath
                contentDescription: {
                  if (root.searchFullPath) {
                    return "已开启检索全路径"
                  } else {
                    return "未开启检索全路径（当前仅检索文件名）"
                  }
                }
                skyIconColor: {
                  if (root.searchFullPath)
                    return "#1890FF"
                  else
                    return "#333"
                }
                onClicked: {
                  root.searchFullPath = !root.searchFullPath
                }
              }
            }
          }
        }
        skyOptions: [
          {
            title: (formatTranslation(fileName.value)),
            disabled: true
          },
          {
            title: "常用文档(.doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf)",
            value: ".doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf",
            group: "常用词",
            disabled: false
          },
          {
            title: "常用文档及压缩包(.doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf .7z .rar .zip)",
            value: ".doc .docx .xls .xlsx .ppt .pptx .wps .et .dps .pdf .7z .rar .zip",
            group: "常用词",
            disabled: false
          },
          {
            title: "常用图片(.png .jpg .tif .bmp)",
            value: ".png .jpg .tif .bmp",
            group: "常用词",
            disabled: false
          },
          {
            title: "秘密 机密 绝密",
            group: "常用词",
            disabled: false
          }
        ].concat(root.filenamesHistory)

        onSkyTabPressed: {
          fullText.forceActiveFocus()
        }
        // @@@@liukai 文件名输入框回车触发文件内容检查
        onValueChanged: {
          if (root.fileNameQuery == value) return;
          root.fileNameQuery = value;
        }

        Keys.onEnterPressed: {
          if (viewModel.searching) return;
          // 右 enter
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
          closePopup()
        }

        Keys.onReturnPressed: {
          if (viewModel.searching) return;
          // 左 enter
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
          closePopup()
        }
      }
    }

    RowLayout{
      id: fullTextRow
      x: {
        if (previewerEnabled) {
          return 0
        } else {
          return fileNameRow.width + 20
        }
      }
      y: {
        if (previewerEnabled) {
          return fileNameRow.height + 11
        } else {
          return 0
        }
      }
      width: {
        if (previewerEnabled) {
          return parent.width - searchButton.width - 20
        } else {
          return (parent.width - searchButton.width) / 2 - 20
        }
      }
      height: {
        if (previewerEnabled) {
          return 30
        } else {
          return parent.height
        }
      }
      SkyTitle {
        text: qsTr("文件内容: ")
        Layout.preferredWidth: 80
        Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
        Layout.fillHeight: true
        description: qsTr("多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font><br>可以在关键词前加<font color=\"red\">+</font>或<font color=\"red\">-</font>表示必须包含或者不得包含关键词，如：<font color=\"red\">财务 绩效 考核 +内部文件</font><br>表示结果中<font color=\"red\">包含财务或绩效或考核，同时必须包含内部文件</font>")
      }
      SkyInpuSelect {
        id: fullText
        Layout.preferredWidth: fileName.width
        // Layout.fillWidth: true
        implicitHeight: parent.height
        placeholderText: "在此输入文件内容搜索条件"
        contentDescription: "多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font>"
        skyOptions: [
          {
            title: (formatTranslation(fullText.value)),
            disabled: true
          },
          {
            title: "秘密 机密 绝密",
            group: "常用词",
            disabled: false
          },
          {
            title: "机密 绝密",
            group: "常用词",
            disabled: false
          },
          {
            title: "绝密",
            group: "常用词",
            disabled: false
          }
        ].concat(root.fullTextsHistory)
        skyRightMeun: Component {
          id: fullTextRightMeun
          RowLayout {
            width: 25
            height: 25
            spacing: 0
            Item {
              Layout.fillWidth: true
              Layout.fillHeight: true
              
              SkyButton {
                anchors.fill: parent
                iconSize: skyTheme.fontSize
                type: "text"
                shape: "circle"
                enabledHovered: false
                iconSource: SkyIcons.Favicon2
                showDisabledIcon: !root.enableCompression
                contentDescription: {
                  if (root.enableCompression) {
                    return "已开启压缩包内容提取"
                  } else {
                    return "未开启压缩包内容提取"
                  }
                }
                skyIconColor: {
                  if (root.enableCompression)
                    return "#1890FF"
                  else
                    return "#333"
                }
                onClicked: {
                  const value = !root.enableCompression
                  if (value) {
                    showWarning("开启压缩包检索会降低检索速度")
                  }
                  root.setEnableCompression(value)
                }
              }
            }
          }
        }

        onSkyTabPressed: {
          fileName.forceActiveFocus()
        }
        // @@@@liukai 内容输入框回车触发文件内容检查
        Keys.onEnterPressed: {
          if (viewModel.searching) return;
          // 右 enter
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
          closePopup()
        }

        Keys.onReturnPressed: {
          if (viewModel.searching) return;
          // 左 enter
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
          closePopup()
        }
      }
    }

    // 高级筛选展开按钮
    SkyButton {
      id: advancedFilterButton
      width: 30
      x: {
        if(previewerEnabled) {
          return fileNameRow.width + 20
        } else {
          return fileNameRow.width + fullTextRow.width + 40
        }
      }
      y: {
        if(previewerEnabled) {
          return fileNameRow.height + 11
        } else {
          return 0
        }
      }
      height: {
        if(previewerEnabled) {
          return 30
        } else {
          return parent.height
        }
      }
      type: "text"
      iconSource: root.showAdvancedFilters ? SkyIcons.Up : SkyIcons.Down
      contentDescription: root.showAdvancedFilters ? "收起高级筛选" : "展开高级筛选"
      onClicked: {
        root.showAdvancedFilters = !root.showAdvancedFilters
      }
    }

    SkyButton {
      id: searchButton
      width: 62
      x: {
        if(previewerEnabled) {
          return fileNameRow.width + advancedFilterButton.width + 25
        } else {
          return fileNameRow.width + fullTextRow.width + advancedFilterButton.width + 45
        }
      }
      y: {
        if(previewerEnabled) {
          return fileNameRow.height + 11
        } else {
          return 0
        }
      }
      height: {
        if(previewerEnabled) {
          return 30
        } else {
          return parent.height
        }
      }
      type: "primary"
      disabled: viewModel.indexState === MainWindowViewModel.Loading || (viewModel.searching &&  viewModel.filterState ===1) || viewModel.filterState === 2
      text: {
        if (viewModel.indexState === MainWindowViewModel.Loading && viewModel.searching) return "载入中";
        if (viewModel.filterState === 1 || viewModel.filterState === 2) return "停止"
        return "搜索"
      }
      // @@@@liukai 搜索按钮触发文件内容检查
      onClicked: {
        if (viewModel.filterState === 1 || viewModel.filterState === 2) {
          // 停止过滤时也需要折叠preview
          root.previewShouldCollapse()
          viewModel.endBackgroundIndexFileContentFiltering()
        } else {
          root.formChage(fileName.value, fullText.value, root.caseInsensive, root.searchFullPath,
                         root.createTimeFrom, root.createTimeTo, root.modifyTimeFrom, root.modifyTimeTo,
                         root.fileSizeMin, root.fileSizeMax)
        }
        forceActiveFocus()
      }
    }

    // 高级筛选区域
    Rectangle {
      id: advancedFiltersArea
      visible: root.showAdvancedFilters
      width: parent.width
      height: visible ? 120 : 0
      y: {
        if(previewerEnabled) {
          return fullTextRow.y + fullTextRow.height + 15
        } else {
          return parent.height + 15
        }
      }
      color: "#f8f9fa"
      border.color: "#e9ecef"
      border.width: 1
      radius: 4

      ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10

        // 第一行：创建时间筛选
        RowLayout {
          Layout.fillWidth: true
          spacing: 10

          SkyTitle {
            text: qsTr("创建时间: ")
            Layout.preferredWidth: 80
            Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
            description: qsTr("筛选指定创建时间范围内的文件")
          }

          SkyTitle {
            text: qsTr("从")
            Layout.preferredWidth: 20
          }

          Rectangle {
            Layout.preferredWidth: 120
            Layout.preferredHeight: 25
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: createTimeFromInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              text: root.createTimeFrom
              onTextChanged: root.createTimeFrom = text
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "yyyy-MM-dd"
              color: "#999"
              visible: createTimeFromInput.text.length === 0
            }
          }

          SkyTitle {
            text: qsTr("到")
            Layout.preferredWidth: 20
          }

          Rectangle {
            Layout.preferredWidth: 120
            Layout.preferredHeight: 25
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: createTimeToInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              text: root.createTimeTo
              onTextChanged: root.createTimeTo = text
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "yyyy-MM-dd"
              color: "#999"
              visible: createTimeToInput.text.length === 0
            }
          }

          Item { Layout.fillWidth: true }
        }

        // 第二行：修改时间筛选
        RowLayout {
          Layout.fillWidth: true
          spacing: 10

          SkyTitle {
            text: qsTr("修改时间: ")
            Layout.preferredWidth: 80
            Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
            description: qsTr("筛选指定修改时间范围内的文件")
          }

          SkyTitle {
            text: qsTr("从")
            Layout.preferredWidth: 20
          }

          Rectangle {
            Layout.preferredWidth: 120
            Layout.preferredHeight: 25
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: modifyTimeFromInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              text: root.modifyTimeFrom
              onTextChanged: root.modifyTimeFrom = text
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "yyyy-MM-dd"
              color: "#999"
              visible: modifyTimeFromInput.text.length === 0
            }
          }

          SkyTitle {
            text: qsTr("到")
            Layout.preferredWidth: 20
          }

          Rectangle {
            Layout.preferredWidth: 120
            Layout.preferredHeight: 25
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: modifyTimeToInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              text: root.modifyTimeTo
              onTextChanged: root.modifyTimeTo = text
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "yyyy-MM-dd"
              color: "#999"
              visible: modifyTimeToInput.text.length === 0
            }
          }

          Item { Layout.fillWidth: true }
        }

        // 第三行：文件大小筛选
        RowLayout {
          Layout.fillWidth: true
          spacing: 10

          SkyTitle {
            text: qsTr("文件大小: ")
            Layout.preferredWidth: 80
            Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
            description: qsTr("筛选指定大小范围内的文件，单位为KB")
          }

          SkyTitle {
            text: qsTr("最小")
            Layout.preferredWidth: 30
          }

          Rectangle {
            Layout.preferredWidth: 80
            Layout.preferredHeight: 25
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: fileSizeMinInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              text: root.fileSizeMin
              onTextChanged: root.fileSizeMin = text
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "KB"
              color: "#999"
              visible: fileSizeMinInput.text.length === 0
            }
          }

          SkyTitle {
            text: qsTr("最大")
            Layout.preferredWidth: 30
          }

          Rectangle {
            Layout.preferredWidth: 80
            Layout.preferredHeight: 25
            border.color: "#ccc"
            border.width: 1
            radius: 2

            TextInput {
              id: fileSizeMaxInput
              anchors.fill: parent
              anchors.margins: 5
              verticalAlignment: TextInput.AlignVCenter
              text: root.fileSizeMax
              onTextChanged: root.fileSizeMax = text
            }

            Text {
              anchors.left: parent.left
              anchors.leftMargin: 5
              anchors.verticalCenter: parent.verticalCenter
              text: "KB"
              color: "#999"
              visible: fileSizeMaxInput.text.length === 0
            }
          }

          Item { Layout.fillWidth: true }
        }
      }
    }
  }

  function formatTranslation(value) {
    if (!value) return "<font size=\"12px\" color=\"#666\" >多个关键词之间用<font color=\"red\">空格</font>分割，表示或的关系，比如：<font color=\"red\">秘密 机密 绝密</font></font>"
    if (/[\*,\?]/.test(value)) return "<font color=\"red\" size=\"12px\">不支持通配符</font></font>"
    const arr = value.split(" ");
    let result = "<font color=\"#666\" size=\"12px\">"
    const forceStr = arr.filter(v => v[0] === "+").map(v => v.substr(1)).filter(v => !!v).join(" 和 ")
    const includeStr = arr.filter(v => v[0] !== "+" && v[0] !== "-").filter(v => !!v).join(" 或 ")
    const excludeArr = arr.filter(v => v[0] === "-").map(v => v.substr(1)).filter(v => !!v).join(" 和 ")
    if (!!forceStr) {
      result += "必须包含" + forceStr
    }

    if (!!includeStr) {
      if (!!forceStr) result += "，"
      result += "包含" + includeStr
    }

    if (!!excludeArr) {
      if (!!forceStr || !!includeStr) result += "，"
      result += "不得包含" + excludeArr
    }

    result += "</font>";
    return result
  }
}