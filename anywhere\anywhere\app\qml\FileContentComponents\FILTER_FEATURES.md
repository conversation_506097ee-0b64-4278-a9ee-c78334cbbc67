# 文件内容搜索 - 高级筛选功能

## 新增功能概述

在原有的文件名和文件内容搜索基础上，新增了三个高级筛选条件：

1. **创建时间筛选**：按文件创建时间范围筛选
2. **修改时间筛选**：按文件修改时间范围筛选  
3. **文件大小筛选**：按文件大小范围筛选（单位：KB）

## 功能特性

### 用户界面改进

- 在搜索框右侧添加了展开/折叠按钮（向上/向下箭头图标）
- 点击展开按钮显示高级筛选选项面板
- 高级筛选面板包含三行筛选条件，布局清晰美观
- 优化了输入框和按钮的宽度分配，确保所有控件都能正常显示在界面内
- 调整了文件名和文件内容输入框的宽度，为新增的高级筛选按钮预留空间

### 筛选条件

#### 1. 创建时间筛选
- **输入格式**：yyyy-MM-dd（例如：2024-01-01）
- **功能**：筛选指定创建时间范围内的文件
- **字段**：
  - 从：创建时间开始日期
  - 到：创建时间结束日期

#### 2. 修改时间筛选
- **输入格式**：yyyy-MM-dd（例如：2024-01-01）
- **功能**：筛选指定修改时间范围内的文件
- **字段**：
  - 从：修改时间开始日期
  - 到：修改时间结束日期

#### 3. 文件大小筛选
- **输入格式**：数字（单位：KB）
- **功能**：筛选指定大小范围内的文件
- **字段**：
  - 最小：最小文件大小（KB）
  - 最大：最大文件大小（KB）

## 布局优化

### 宽度调整
- 文件名输入框宽度：考虑高级筛选按钮和搜索按钮的空间需求
- 文件内容输入框宽度：与文件名输入框保持一致
- 高级筛选面板：
  - 创建时间输入框：100px
  - 修改时间输入框：100px
  - 文件大小输入框：70px
  - 标签宽度优化：主标签70px，辅助标签15-25px

### 响应式布局
- 支持预览模式和非预览模式的不同布局
- 自动调整控件位置和大小以适应不同的界面状态

## 技术实现

### 前端修改

#### SearchFrom.qml
- 新增属性：
  ```qml
  property string createTimeFrom: ""
  property string createTimeTo: ""
  property string modifyTimeFrom: ""
  property string modifyTimeTo: ""
  property string fileSizeMin: ""
  property string fileSizeMax: ""
  property bool showAdvancedFilters: false
  ```

- 扩展信号：
  ```qml
  signal formChage(string fileName, string fullText, bool caseInsensive, bool searchOnlyFileName, 
                   string createTimeFrom, string createTimeTo, string modifyTimeFrom, string modifyTimeTo,
                   string fileSizeMin, string fileSizeMax)
  ```

- 布局优化：
  ```qml
  width: {
    if (previewerEnabled) {
      return parent.width - searchButton.width - advancedFilterButton.width - 30
    } else {
      return (parent.width - searchButton.width - advancedFilterButton.width) / 2 - 30
    }
  }
  ```

#### ContentLayout.qml
- 更新searchChange信号以包含新的筛选参数
- 添加输入验证逻辑（日期格式、数字格式）
- 调用新的searchWithFilters方法

### 后端修改

#### ViewModel.h
- 新增方法声明：
  ```cpp
  Q_INVOKABLE void searchWithFilters(const QString &fileNameQuery,
                                     const QString &fullTextQuery,
                                     const QString &sortProperty, bool sortOrderAsc,
                                     bool caseInsensive, bool searchFullPath,
                                     const QString &createTimeFrom, const QString &createTimeTo,
                                     const QString &modifyTimeFrom, const QString &modifyTimeTo,
                                     const QString &fileSizeMin, const QString &fileSizeMax);
  ```

#### ViewModel.cc
- 实现searchWithFilters方法
- 当前版本记录筛选条件到日志
- 为后续实现实际筛选逻辑预留接口

## 使用方法

1. 在文件名搜索框输入文件名条件（可选）
2. 在文件内容搜索框输入内容条件（可选）
3. 点击搜索框右侧的展开按钮（向下箭头）
4. 在高级筛选面板中设置筛选条件：
   - 创建时间：输入开始和结束日期
   - 修改时间：输入开始和结束日期
   - 文件大小：输入最小和最大值（KB）
5. 点击搜索按钮执行搜索

## 输入验证

系统会自动验证输入格式：
- 日期必须符合 yyyy-MM-dd 格式
- 文件大小必须是数字
- 格式错误时会显示相应的警告信息

## 示例用法

### 搜索最近一个月的PDF文件
- 文件名：*.pdf
- 创建时间从：2024-11-01
- 创建时间到：2024-12-01

### 搜索大于1MB的机密文档
- 文件内容：机密
- 文件大小最小：1024

### 搜索特定时间段修改的文档
- 文件名：*.doc *.docx
- 修改时间从：2024-01-01
- 修改时间到：2024-06-30

## 后续开发计划

1. **后端筛选逻辑实现**：在搜索引擎中实现实际的时间和大小筛选
2. **性能优化**：优化大量文件的筛选性能
3. **更多筛选条件**：可考虑添加文件类型、文件路径等筛选条件
4. **预设筛选模板**：提供常用的筛选条件组合

## 文件清单

- `SearchFrom.qml` - 主要搜索界面，包含高级筛选UI
- `ContentLayout.qml` - 搜索布局容器，处理搜索逻辑
- `ViewModel.h` - 视图模型头文件，包含新方法声明
- `ViewModel.cc` - 视图模型实现，包含searchWithFilters方法
- `FilterExample.qml` - 功能演示示例文件
- `FILTER_FEATURES.md` - 本说明文档

## 更新日志

### v1.1 - 布局优化
- 调整了输入框和按钮的宽度分配
- 优化了高级筛选面板的布局
- 确保所有控件都能正常显示在界面内
- 减少了标签的宽度以节省空间
