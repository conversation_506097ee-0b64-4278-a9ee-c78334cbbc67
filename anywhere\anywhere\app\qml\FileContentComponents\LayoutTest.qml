import QtQuick 2.12
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// 简单的布局测试文件，用于验证高级筛选功能的布局
ApplicationWindow {
    id: window
    width: 1200
    height: 800
    visible: true
    title: "高级筛选布局测试"

    Rectangle {
        anchors.fill: parent
        color: "#f0f0f0"
        
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 20
            
            Text {
                text: "文件内容搜索 - 高级筛选布局测试"
                font.pixelSize: 18
                font.bold: true
                Layout.alignment: Qt.AlignHCenter
            }
            
            // 模拟搜索区域
            Rectangle {
                Layout.fillWidth: true
                Layout.preferredHeight: 200
                color: "white"
                border.color: "#ccc"
                border.width: 1
                radius: 4
                
                ColumnLayout {
                    anchors.fill: parent
                    anchors.margins: 10
                    spacing: 10
                    
                    // 第一行：文件名搜索
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 10
                        
                        Text {
                            text: "文件名:"
                            Layout.preferredWidth: 80
                        }
                        
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 30
                            border.color: "#ccc"
                            border.width: 1
                            
                            TextInput {
                                anchors.fill: parent
                                anchors.margins: 5
                                verticalAlignment: TextInput.AlignVCenter
                                text: "*.pdf"
                            }
                        }
                        
                        Button {
                            text: "↓"
                            Layout.preferredWidth: 30
                            Layout.preferredHeight: 30
                        }
                        
                        Button {
                            text: "搜索"
                            Layout.preferredWidth: 62
                            Layout.preferredHeight: 30
                        }
                    }
                    
                    // 第二行：文件内容搜索
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 10
                        
                        Text {
                            text: "文件内容:"
                            Layout.preferredWidth: 80
                        }
                        
                        Rectangle {
                            Layout.fillWidth: true
                            Layout.preferredHeight: 30
                            border.color: "#ccc"
                            border.width: 1
                            
                            TextInput {
                                anchors.fill: parent
                                anchors.margins: 5
                                verticalAlignment: TextInput.AlignVCenter
                                text: "机密"
                            }
                        }
                        
                        Item {
                            Layout.preferredWidth: 30
                        }
                        
                        Item {
                            Layout.preferredWidth: 62
                        }
                    }
                    
                    // 高级筛选面板
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.preferredHeight: 120
                        color: "#f8f9fa"
                        border.color: "#e9ecef"
                        border.width: 1
                        radius: 4
                        
                        ColumnLayout {
                            anchors.fill: parent
                            anchors.margins: 10
                            spacing: 10
                            
                            // 创建时间
                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 10
                                
                                Text {
                                    text: "创建时间:"
                                    Layout.preferredWidth: 70
                                }
                                
                                Text {
                                    text: "从"
                                    Layout.preferredWidth: 15
                                }
                                
                                Rectangle {
                                    Layout.preferredWidth: 100
                                    Layout.preferredHeight: 25
                                    border.color: "#ccc"
                                    border.width: 1
                                    
                                    TextInput {
                                        anchors.fill: parent
                                        anchors.margins: 5
                                        verticalAlignment: TextInput.AlignVCenter
                                        text: "2024-01-01"
                                    }
                                }
                                
                                Text {
                                    text: "到"
                                    Layout.preferredWidth: 15
                                }
                                
                                Rectangle {
                                    Layout.preferredWidth: 100
                                    Layout.preferredHeight: 25
                                    border.color: "#ccc"
                                    border.width: 1
                                    
                                    TextInput {
                                        anchors.fill: parent
                                        anchors.margins: 5
                                        verticalAlignment: TextInput.AlignVCenter
                                        text: "2024-12-31"
                                    }
                                }
                                
                                Item { Layout.fillWidth: true }
                            }
                            
                            // 修改时间
                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 10
                                
                                Text {
                                    text: "修改时间:"
                                    Layout.preferredWidth: 70
                                }
                                
                                Text {
                                    text: "从"
                                    Layout.preferredWidth: 15
                                }
                                
                                Rectangle {
                                    Layout.preferredWidth: 100
                                    Layout.preferredHeight: 25
                                    border.color: "#ccc"
                                    border.width: 1
                                    
                                    TextInput {
                                        anchors.fill: parent
                                        anchors.margins: 5
                                        verticalAlignment: TextInput.AlignVCenter
                                        text: "2024-06-01"
                                    }
                                }
                                
                                Text {
                                    text: "到"
                                    Layout.preferredWidth: 15
                                }
                                
                                Rectangle {
                                    Layout.preferredWidth: 100
                                    Layout.preferredHeight: 25
                                    border.color: "#ccc"
                                    border.width: 1
                                    
                                    TextInput {
                                        anchors.fill: parent
                                        anchors.margins: 5
                                        verticalAlignment: TextInput.AlignVCenter
                                        text: "2024-12-31"
                                    }
                                }
                                
                                Item { Layout.fillWidth: true }
                            }
                            
                            // 文件大小
                            RowLayout {
                                Layout.fillWidth: true
                                spacing: 10
                                
                                Text {
                                    text: "文件大小:"
                                    Layout.preferredWidth: 70
                                }
                                
                                Text {
                                    text: "最小"
                                    Layout.preferredWidth: 25
                                }
                                
                                Rectangle {
                                    Layout.preferredWidth: 70
                                    Layout.preferredHeight: 25
                                    border.color: "#ccc"
                                    border.width: 1
                                    
                                    TextInput {
                                        anchors.fill: parent
                                        anchors.margins: 5
                                        verticalAlignment: TextInput.AlignVCenter
                                        text: "1024"
                                    }
                                }
                                
                                Text {
                                    text: "最大"
                                    Layout.preferredWidth: 25
                                }
                                
                                Rectangle {
                                    Layout.preferredWidth: 70
                                    Layout.preferredHeight: 25
                                    border.color: "#ccc"
                                    border.width: 1
                                    
                                    TextInput {
                                        anchors.fill: parent
                                        anchors.margins: 5
                                        verticalAlignment: TextInput.AlignVCenter
                                        text: "10240"
                                    }
                                }
                                
                                Item { Layout.fillWidth: true }
                            }
                        }
                    }
                }
            }
            
            Text {
                text: "布局测试说明：\n" +
                      "1. 检查所有控件是否在界面内正常显示\n" +
                      "2. 验证输入框宽度是否合适\n" +
                      "3. 确认按钮位置是否正确\n" +
                      "4. 测试高级筛选面板的布局"
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }
            
            Item { Layout.fillHeight: true }
        }
    }
}
