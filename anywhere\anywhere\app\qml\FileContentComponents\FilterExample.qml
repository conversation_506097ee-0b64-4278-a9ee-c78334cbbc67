import QtQuick.Controls 2.15
import QtQuick 2.12
import QtQuick.Layouts 1.15
import SkyUi 1.0
import AnywhereQmlModule 1.0

// 这是一个演示新筛选功能的示例文件
// 展示如何使用创建时间、修改时间和文件大小筛选

Item {
  id: root
  width: 800
  height: 600

  property alias searchFrom: searchFromComponent

  ColumnLayout {
    anchors.fill: parent
    anchors.margins: 20
    spacing: 20

    // 标题
    Text {
      text: "文件内容搜索 - 高级筛选功能演示"
      font.pixelSize: 18
      font.bold: true
      Layout.alignment: Qt.AlignHCenter
    }

    // 说明文本
    Text {
      text: "新增功能：\n" +
            "1. 创建时间筛选：可以指定文件创建时间范围\n" +
            "2. 修改时间筛选：可以指定文件修改时间范围\n" +
            "3. 文件大小筛选：可以指定文件大小范围（单位：KB）\n\n" +
            "使用方法：\n" +
            "- 点击搜索框右侧的展开按钮显示高级筛选选项\n" +
            "- 日期格式：yyyy-MM-dd（例如：2024-01-01）\n" +
            "- 文件大小：输入数字，单位为KB"
      wrapMode: Text.WordWrap
      Layout.fillWidth: true
      Layout.preferredHeight: 120
    }

    // 搜索组件
    Rectangle {
      Layout.fillWidth: true
      Layout.preferredHeight: 200
      border.color: "#e0e0e0"
      border.width: 1
      radius: 4

      SearchFrom {
        id: searchFromComponent
        anchors.fill: parent
        anchors.margins: 10
        previewerEnabled: true

        // 示例数据
        filenamesHistory: [
          "财务报表",
          "会议纪要",
          "项目文档"
        ]
        fullTextsHistory: [
          "机密",
          "内部文件",
          "重要通知"
        ]

        onFormChage: {
          console.log("搜索参数:")
          console.log("文件名:", fileName)
          console.log("文件内容:", fullText)
          console.log("区分大小写:", caseInsensive)
          console.log("搜索全路径:", searchOnlyFileName)
          console.log("创建时间从:", createTimeFrom)
          console.log("创建时间到:", createTimeTo)
          console.log("修改时间从:", modifyTimeFrom)
          console.log("修改时间到:", modifyTimeTo)
          console.log("最小文件大小:", fileSizeMin, "KB")
          console.log("最大文件大小:", fileSizeMax, "KB")
          
          // 这里可以调用实际的搜索逻辑
          resultText.text = "搜索已执行，参数已记录到控制台"
        }

        onSetEnableCompression: {
          console.log("压缩包检索:", enableCompression)
        }
      }
    }

    // 结果显示区域
    Rectangle {
      Layout.fillWidth: true
      Layout.fillHeight: true
      border.color: "#e0e0e0"
      border.width: 1
      radius: 4

      ScrollView {
        anchors.fill: parent
        anchors.margins: 10

        Text {
          id: resultText
          text: "搜索结果将显示在这里...\n\n" +
                "使用示例：\n" +
                "1. 在文件名框输入：*.pdf\n" +
                "2. 在文件内容框输入：机密\n" +
                "3. 点击高级筛选展开按钮\n" +
                "4. 设置创建时间：从 2024-01-01 到 2024-12-31\n" +
                "5. 设置文件大小：最小 100KB，最大 10000KB\n" +
                "6. 点击搜索按钮"
          wrapMode: Text.WordWrap
          width: parent.width
        }
      }
    }

    // 使用说明
    Text {
      text: "注意：当前版本的高级筛选功能已添加到UI中，后端筛选逻辑需要进一步实现。"
      color: "#666666"
      font.italic: true
      Layout.alignment: Qt.AlignHCenter
    }
  }
}
