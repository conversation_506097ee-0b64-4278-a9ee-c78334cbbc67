#pragma once

#include <QString>
#include <QObject>
#include <atomic>
#include <thread>
#include <memory>
#include <filesystem>
#include "database/Database.h"
#include "database/FileChangeLogTable.h"
#include "database/TaskResultTable.h"
#include "database/TaskTable.h"
#include "core/services/FileContentIndexService.h"
#include "core/services/SearchEngineService.h"
#include "core/services/ConfigService.h"
#include "CancellationToken.h"
#include "FilePathFilter.h"
#include "FileExtFilter.h"
#include "mpm/AutoMpmEngine.h"
#include "TermsQueryMatchEngine.h"
#include "CheckerUtils.h"
#include "Highlighter.h"
#include "parser/FileFormatDetector.h"
#include "database/UploadFileTable.h"
#include "KeywordExtractor.h"
#include <QNetworkAccessManager>

namespace anywhere {
namespace app {

/**
 * 增量检索处理器
 * 负责持续监控file_change_log表中的文件变更，并进行增量检索处理
 * 独立运行，不依赖于BackgroundModel的生命周期
 * 实现DocProcessor接口，作为FileContentIndexService的文档处理器
 */
class IncrementalSearchProcessor : public core::DocProcessor, public std::enable_shared_from_this<IncrementalSearchProcessor> {
public:
    explicit IncrementalSearchProcessor(const std::string& taskId);
    ~IncrementalSearchProcessor();

    // 启动和停止增量检索
    void start();
    void stop();
    bool isRunning() const { return running_.load(); }

    // 更新搜索参数（当任务变化时调用）
    void updateSearchParameters(const FilePathFilter& pathFilter,
                               const FileExtFilter& extFilter,
                               std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fileNameMatcher,
                               std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fileNameExcludeMatcher,
                               std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fullTextMatcher,
                               std::optional<TermsQueryMatcher<AutoMpmEngine>>&& fullTextExcludeMatcher,
                               std::optional<Highlighter>&& filePathHighlighter,
                               std::optional<Highlighter>&& fullTextHighlighter,
                               std::shared_ptr<core::ConfigService> configService,
                               const AdvancedChecker& advancedChecker,
                               const Query* parsedFullTextQuery = nullptr);

    // 设置网络访问管理器
    void setNetworkAccessManager(std::shared_ptr<QNetworkAccessManager> networkAccessManager) {
        networkAccessManager_ = networkAccessManager;
    }

private:
    // 主处理循环
    void processLoop();
    
    // 处理单个文件变更记录
    void processFileChange(const FileChangeLogTable::FileChangeRecord& record);

    // 文件过滤
    bool shouldProcessFile(const std::filesystem::path& filePath);

    // 检索匹配
    bool matchesSearchCriteria(const std::filesystem::path& filePath,
                              const std::u8string& content);



    // 保存检索结果
    void saveSearchResult(const std::filesystem::path& filePath,
                         const std::u8string& content,
                         const core::SearchEngineService::Document& doc);

    // DocProcessor接口实现
    void onDocIndexed(DocId id, const std::filesystem::path& path,
                     const std::u8string& text,
                     std::optional<core::SearchEngineService::Document> doc,
                     void* context) override;

    bool filterFile(const std::filesystem::path& path) override;
    bool filterDoc(const core::SearchEngineService::Document& doc) override;
    bool RecoverFile(uint64_t inode, int indexId, int volumId, const std::string& path) override;
    std::string ConstructNewFileName(const std::string& filename, uint64_t inode) override;

private:
    std::string taskId_;
    std::atomic<bool> running_;
    std::thread processingThread_;
    
    // 数据库相关
    Database db_;
    std::shared_ptr<FileChangeLogTable> fileChangeLogTable_;
    std::shared_ptr<TaskResultTable> taskResultTable_;
    std::shared_ptr<TaskTable> taskTable_;
    
    // 服务依赖
    std::shared_ptr<core::FileContentIndexService> fileContentIndexService_;
    std::shared_ptr<core::SearchEngineService> searchEngineService_;
    std::shared_ptr<core::ConfigService> configService_;
    std::shared_ptr<parser::FileFormatDetector> fileFormatDetector_;
    
    // 搜索参数
    FilePathFilter filePathFilter_;
    FileExtFilter fileExtFilter_;

    // 查询匹配器
    std::optional<TermsQueryMatcher<AutoMpmEngine>> fileNameMatcher_;
    std::optional<TermsQueryMatcher<AutoMpmEngine>> fileNameExcludeMatcher_;
    std::optional<TermsQueryMatcher<AutoMpmEngine>> fullTextMatcher_;
    std::optional<TermsQueryMatcher<AutoMpmEngine>> fullTextExcludeMatcher_;

    // 高亮器
    std::optional<Highlighter> filePathHighlighter_;
    std::optional<Highlighter> fullTextHighlighter_;

    // 高级检查器
    AdvancedChecker advancedChecker_;

    // 关键词提取器
    KeywordExtractor keywordExtractor_;

    // 保存的查询对象副本（用于关键词提取）
    std::unique_ptr<Query> savedParsedFullTextQuery_;

    // 网络和上传相关
    UploadFileTable uploadFileTable_;
    std::shared_ptr<QNetworkAccessManager> networkAccessManager_;

    // 处理统计
    // std::atomic<size_t> processedCount_;
    // std::atomic<size_t> matchedCount_;
};

} // namespace app
} // namespace anywhere
