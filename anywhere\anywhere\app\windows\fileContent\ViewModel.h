#pragma once

#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <boost/thread/future.hpp>
#include <boost/thread/executor.hpp>
#include <QObject>
#include <QStorageInfo>
#include <QSettings>
#include <QElapsedTimer>
#include <QDebug>
#include <QTimer>

#include "config.h"

#include "core/services/SearchEngineService.h"
#include "core/services/FileContentIndexService.h"
#include "core/services/LicenseService.h"
#include "core/services/CleanFileService.h"
#include "core/services/DeletedFileCollectionService.h"
#include "core/tasks/IndexFileContentTask.h"
#include "QueryParser.h"
#include "../../services/FileIconProvider.h"
#include "../../services/RestartManager.h"
#include "../../services/Manual.h"
#include "SearchResultTableModel.h"
#include "PendingFilesTableModel.h"
#include "ContentPreviewViewModel.h"
#include "../QTopHits.h"
#include "../QDocSet.h"
#include "../SearchHistory.h"
#include "Highlighter.h"
#include "mpm/AutoMpmEngine.h"
#include "TermsQueryMatchEngine.h"
#include "../QLicense.h"
#include <mutex>

namespace anywhere {
namespace app {

class ViewModel;

class ViewModel : public QObject {
  Q_OBJECT
  // clang-format off
  Q_PROPERTY(IndexState indexState READ indexState NOTIFY indexStateChanged)
  Q_PROPERTY(QString indexErrorReason READ indexErrorReason NOTIFY indexErrorReasonChanged)
  Q_PROPERTY(unsigned int indexedFilesCnt READ indexedFilesCnt NOTIFY indexedFilesCntChanged)
  Q_PROPERTY(double searchTime READ searchTime NOTIFY searchTimeChanged)
  Q_PROPERTY(QVector<QString> roots READ roots NOTIFY rootsChanged)
  Q_PROPERTY(float indexProgress READ indexProgress NOTIFY indexProgressChanged)
  Q_PROPERTY(bool searching READ searching NOTIFY searchingChanged)
  Q_PROPERTY(int filterState READ filterState NOTIFY filterStateChanged)
  Q_PROPERTY(QString fileNameQuery READ fileNameQuery NOTIFY fileNameQueryChanged)
  Q_PROPERTY(QString fullTextQuery READ fullTextQuery NOTIFY fullTextQueryChanged)
  Q_PROPERTY(QString sortProperty READ sortProperty NOTIFY sortPropertyChanged)
  Q_PROPERTY(bool sortOrderAsc READ sortOrderAsc NOTIFY sortOrderAscChanged)
  Q_PROPERTY(bool caseInsensive READ caseInsensive NOTIFY caseInsensiveChanged)
  Q_PROPERTY(bool searchFullPath READ searchFullPath NOTIFY searchFullPathChanged)
  Q_PROPERTY(QTopHits* topHits READ
                 topHits NOTIFY topHitsChanged)
  Q_PROPERTY(QDocSet* pendingFilesSet READ
                 pendingFilesSet NOTIFY pendingFilesSetChanged)
  Q_PROPERTY(unsigned int activeDocId READ activeDocId WRITE setActiveDocId NOTIFY activeDocIdChanged)
  Q_PROPERTY(const unsigned int invalidDocId READ invalidDocId NOTIFY invalidDocIdChanged)
  Q_PROPERTY(QVector<QString> filenamesHistory READ filenamesHistory NOTIFY filenamesHistoryChanged)
  Q_PROPERTY(QVector<QString> fullTextsHistory READ fullTextsHistory NOTIFY fullTextsHistoryChanged)
  Q_PROPERTY(QLicense* license READ license CONSTANT)
  Q_PROPERTY(bool licenseUnavailable READ licenseUnavailable NOTIFY licenseUnavailableChanged)
  Q_PROPERTY(bool enableCompression READ enableCompression NOTIFY enableCompressionChanged)
  Q_PROPERTY(QString message READ message NOTIFY messageChanged)
  Q_PROPERTY(bool enableTrialVersion READ enableTrialVersion CONSTANT)
  Q_PROPERTY(bool skipLicense READ skipLicense CONSTANT)
  // Q_PROPERTY(int deletedFileCount READ
  //   deletedFileCount NOTIFY deletedFileCountChanged)
  // clang-format on
public:
  enum IndexState { Init, Loading, Success, Error };
  Q_ENUM(IndexState)
  enum FilterState { Idle, Processing, Canceling, Canceled};

  // 默认构造函数，用于需要搜索功能的场景（如viewModel）
  ViewModel();

  // 带参数的构造函数，可以控制是否需要加载搜索索引
  // needSearchIndex: true表示需要搜索功能，false表示只处理已删除文件等不需要搜索的场景
  explicit ViewModel(bool needSearchIndex);

  ~ViewModel();

  IndexState indexState() const { return indexState_; }
  const QString &indexErrorReason() const { return indexErrorReason_; }
  float indexProgress() const { return indexProgress_; }
  unsigned int indexedFilesCnt() const { return indexedFilesCnt_; }
  // 搜索耗时 单位 s
  const double searchTime() const { return searchTime_; }
  const QVector<QString> &roots() const { return roots_; }

  const QVector<QString> &filenamesHistory() const { return filenamesHistory_; }

  const QVector<QString> &fullTextsHistory() const { return fullTextsHistory_; }

  bool searching() const { return searching_; }
  int filterState() const { return filterState_; }
  unsigned int activeDocId() const { return activeDocId_; }

  const QString &fileNameQuery() const { return fileNameQuery_; }
  const QString &fullTextQuery() const { return fullTextQuery_; }
  const QString &sortProperty() const { return sortProperty_; }
  bool sortOrderAsc() const { return sortOrderAsc_; }
  bool caseInsensive() const { return caseInsensive_; }
  bool searchFullPath() const { return searchFullPath_; }

  const unsigned int invalidDocId() const { return invalidDocId_; }

  QTopHits *topHits() const { return topHits_.get(); }

  void setActiveDocId(unsigned int docId);

  QDocSet *pendingFilesSet() { return pendingFilesSet_.get(); }

  // int deletedFileCount() const { return deletedFileCount_; }

  QLicense *license() { return license_.get(); }

  bool licenseUnavailable() const { return licenseUnavailable_; }

  bool enableCompression() const { return enableCompression_; }
  // @@@@liukai 开启试用版本，但并无调用
  bool enableTrialVersion() const {
    return true;//!!ANYWHERE_ENABLE_TRIAL_VERSION;
  }
  void clean(const std::filesystem::path &fileName);
  // 是否跳过授权
  bool skipLicense() const {
    return configService_->skipLicenseEnabled();
  }
  bool needToParse();
  void setSearching(bool);
  void setFilterState(int);
  /**
   * @brief 程序中的错误信息
   */
  const QString &message() const { return message_; }

  Q_INVOKABLE void search(const QString &fileNameQuery,
                          const QString &fullTextQuery,
                          const QString &sortProperty, bool sortOrderAsc,
                          bool caseInsensive, bool searchFullPath);

  Q_INVOKABLE void searchWithFilters(const QString &fileNameQuery,
                                     const QString &fullTextQuery,
                                     const QString &sortProperty, bool sortOrderAsc,
                                     bool caseInsensive, bool searchFullPath,
                                     const QString &createTimeFrom, const QString &createTimeTo,
                                     const QString &modifyTimeFrom, const QString &modifyTimeTo,
                                     const QString &fileSizeMin, const QString &fileSizeMax);

  Q_INVOKABLE void searchDeletedFiles(const QString &fileNameQuery,
                            const QString &fullTextQuery,
                            const QString &sortProperty, bool sortOrderAsc,
                            bool caseInsensive, bool searchFullPath);

  Q_INVOKABLE void close();

  Q_INVOKABLE void restart();

  Q_INVOKABLE void setEnableCompression(bool enableCompression);

  // Q_INVOKABLE bool openManual();

  /**
   * 距离上次更新索引所过的时间 单位: s
  */
  Q_INVOKABLE int elapsedSecondsSinceIndexUpdated();

  Q_INVOKABLE void endBackgroundIndexFileContentFiltering();

Q_SIGNALS:
  void indexStateChanged(IndexState newState);
  void indexErrorReasonChanged(const QString &indexErrorReason);
  void indexProgressChanged(float);
  void indexedFilesCntChanged(unsigned int);
  void searchTimeChanged(const double);
  void rootsChanged(const QVector<QString> &);
  void filenamesHistoryChanged(const QVector<QString> &);
  void fullTextsHistoryChanged(const QVector<QString> &);

  void searchingChanged(bool);
  void filterStateChanged(int);
  void fileNameQueryChanged(const QString &);
  void fullTextQueryChanged(const QString &);
  void sortPropertyChanged(const QString &);
  void sortOrderAscChanged(bool);
  void caseInsensiveChanged(bool);
  void searchFullPathChanged(bool);

  void pendingFilesTableModelChanged(PendingFilesTableModel *);
  void activeDocIdChanged(unsigned int docId);
  void topHitsChanged(QTopHits *);
  void pendingFilesSetChanged(QDocSet *);
  void invalidDocIdChanged(const unsigned int docId);

  void licenseUnavailableChanged(bool);

  void enableCompressionChanged(bool);

  void messageChanged(const QString &);
  void deletedFileCountChanged(int);

private:
  mutable std::mutex filterMutex_;
  void setIndexState(IndexState state);
  void setIndexErrorReason(const QString &indexErrorReason);
  void setIndexProgress(float progress);
  void setIndexedFilesCnt(unsigned int);
  void setRoots(const std::vector<std::filesystem::path> &roots);
  void setLicenseUnavailable(bool);

  // 删除文件收集相关方法
  void waitForCollectDeletedFilesCompletion(const CancellationToken& cancellationToken);
  void waitForDeletedFilesTableCreation(const CancellationToken& cancellationToken);

  // 统一的UI更新相关方法
  void performUIUpdate();
  void addHitToBatch(search::Hit &&hit);
  void stopUIUpdateTimer(); // 优化：统一的定时器停止方法

 
  // 设置搜索耗时
  void startSearchTime();
  void endSearchTime();
  void setFileNameQuery(const QString &fileNameQuery);
  void setFullTextQuery(const QString &fullTextQuery);
  void setSortProperty(const QString &);
  void setSortOrderAsc(bool);
  void setCaseInsensive(bool);
  void setSearchFullPath(bool);
  void addFileNameHistory(const QString &);
  void addFullTextHistory(const QString &);
  std::optional<TermsQueryMatcher<AutoMpmEngine>>
  buildFullTextTermsMatcher() const;
  std::optional<TermsQueryMatcher<AutoMpmEngine>>
  buildFileNameTermsMatcher(bool caseInsensive) const;

  void setTopHits(std::unique_ptr<QTopHits>);
  void setPendingFilesSet(std::unique_ptr<QDocSet>);
  // void setDeletedFileCount(int count);

  void setMessage(const QString &message);

  void onSearchEngineStateChanged(core::SearchEngineService::State state);

  void onLoadIndexProgressChanged(float progress);
  void onFileContentIndexed(DocId id);

  void onIndexCommited(core::FileContentIndexService::OpStamp opstamp);
  void
  onDocIndexed(DocId id, const std::filesystem::path &path,
               const std::u8string &text,
               std::optional<core::FileContentIndexService::OpStamp> opstamp,std::optional<core::SearchEngineService::Document> doc,
               void *context);

  boost::future<void> doSearch(const QString &fileNameQuery,
                               const QString &fullTextQuery,
                               const QByteArray &sortPropertyUtf8, bool sortOrderAsc,
                               bool caseInsensive, bool searchFullPath,
                               const CancellationToken &cancellationToken);

  boost::future<void> doSearchDeletedFiles(const QString &fileNameQuery,
                                const QString &fullTextQuery,
                                const QString &sortProperty, bool sortOrderAsc,
                                bool caseInsensive, bool searchFullPath,
                                const CancellationToken &cancellationToken);

  boost::future<void> reloadFullTextIndex();

  // 过滤尚未全文索引的文档集合, 只保留我们能够支持的文件格式
  boost::future<DocSet>
  filterNotIndexedDocSet(DocSet docSet,
                         const CancellationToken &cancellationToken);

  void startSearchTask(const QString &fileNameQuery,
                       const QString &fullTextQuery,
                       const QString &sortProperty, bool sortOrderAsc,
                       bool caseInsensive, bool searchFullPath);
  void startSearchDeletedFilesTask(const QString &fileNameQuery,
                        const QString &fullTextQuery,
                        const QString &sortProperty, bool sortOrderAsc,
                        bool caseInsensive, bool searchFullPath);
  boost::future<void> endSearchTask();

  void startBackgroundIndexFileContentTask(const DocSet &docSet,
                                           bool includeArchive);
  boost::future<void> endBackgroundIndexFileContentTask();

  void startBatchAddHitsTask(search::TopHits &&hits, const CancellationToken &cancellationToken,
                             const core::SearchEngineService::SearchOptions &searchOptions, const DocSet &docSet);
  IndexState indexState_ = IndexState::Init;
  QString indexErrorReason_;
  float indexProgress_ = 0.0;
  unsigned int indexedFilesCnt_ = 0;            // 索引的文件总数
  QElapsedTimer searchTimer_;                           // 搜索计时器
  double searchTime_ = 0;                       // 搜索耗时
  QVector<QString> roots_ = QVector<QString>(); // 工作盘符
  QVector<QString> filenamesHistory_ = QVector<QString>(); // 文件名搜索历史
  QVector<QString> fullTextsHistory_ = QVector<QString>(); // 全文搜索历史
  boost::signals2::scoped_connection indexStateConnection_;
  boost::signals2::scoped_connection indexProgressConnection_;
  // 文件内容是否启用压缩包索引检录
  bool enableCompression_ = false;
  QString message_;

  // 当前选中的文档ID
  DocId activeDocId_ = 0;
  // 无效的文档ID
  DocId invalidDocId_ = INVALID_DOCID;
  boost::signals2::scoped_connection indexCommitedConnection_;
  boost::signals2::scoped_connection docIndexedConnection_;

  // 最后一次通过 search 函数设置的文件名查询条件
  QString fileNameQuery_;
  std::unique_ptr<Query> parsedFileNameQuery_;
  std::optional<Highlighter> filePathHighlighter_;
  // 最后一次通过 search 函数设置的文件内容查询条件
  QString fullTextQuery_;
  // 最后一次通过 search 函数设置的排序属性
  std::unique_ptr<Query> parsedFullTextQuery_;
  std::optional<TermsQueryMatcher<AutoMpmEngine>> fileNameTermsMatcher_;
  std::optional<TermsQueryMatcher<AutoMpmEngine>> fullTextTermsMatcher_;
  std::optional<Highlighter> fullTextHighlighter_;
  QString sortProperty_;
  // 最后一次通过 search 函数设置的排序顺序
  bool sortOrderAsc_ = false;
  // 最后一次通过 search 函数设置的忽略大小写开关
  bool caseInsensive_ = true;
  // 最后一次通过 search 函数设置的搜索全路径开关
  bool searchFullPath_ = false;

  // 当前用来给界面展示的所有的命中结果
  std::unique_ptr<QTopHits> topHits_;
  // 待索引的文档集合
  std::unique_ptr<QDocSet> pendingFilesSet_;

  // int deletedFileCount_ = 0;
  // std::mutex deletedFileCountMutex_;

  // 当前是否正在搜索中
  bool searching_ = false;
  int filterState_ = FilterState::Idle;
  // 全文索引是否已经过期, 如果过期了, 那么下次搜索前需要重新加载索引
  bool fullTextIndexOutdated_ = false;

  // 搜索历史
  SearchHistory searchHistory_;

  std::shared_ptr<core::FileContentIndexService> fileContentIndexService_;
  std::shared_ptr<core::SearchEngineService> searchEngineService_;
  std::shared_ptr<HighlighterFactory> highlighterFactory_;
  std::shared_ptr<core::ConfigService> configService_;
  std::shared_ptr<core::LicenseService> licenseService_;
  std::shared_ptr<core::CleanFileService> cleanFileService_;

  std::shared_ptr<boost::executors::executor> mainThreadExecutor_;
  std::shared_ptr<boost::executors::executor> ioTheadExecutor_;

  std::shared_ptr<QueryParser> queryParser_;
  std::shared_ptr<FileIconProvider> fileIconProvider_;
  std::shared_ptr<RestartManager> restartManager_;
  // std::shared_ptr<Manual> manual_;

  // 代表前台搜索过程的future, 如果本future完成了, 代表前台搜索任务完成了
  boost::shared_future<void> searchTaskFuture_ = boost::make_shared_future();
  CancellationSource searchTaskCancallationSource_;

  // 代表后台内容索引过程的future, 如果本future完成了, 代表后台索引过程完成了
  boost::shared_future<void> backgroundIndexFileContentTaskFuture_ =
      boost::make_shared_future();

  // 取消后台内容索引过程的令牌
  CancellationSource backgroundIndexFileContentTaskCancallationSource_;

  CancellationSource miscTaskCancellationSource_;

  /**
   * @brief 授权信息, 永远不会为空
   *
   * 授权信息在程序启动时加载, 并且一旦程序启动之后是不会变的,
   * 授权信息更新需要重启程序
   */
  std::unique_ptr<QLicense> license_;
  /**
   * 授权不可用
   *
   * 当授权不可用时, 我们需要在界面上提示用户授权不可用, 并且禁用所有功能
   *
   * 目前只有当前是 UKEY 授权并且 UKEY 被拔出时, 才会出现授权不可用的情况
   */
  bool licenseUnavailable_ = false;
  boost::signals2::scoped_connection licenseUnavailableConnection_;

  boost::signals2::scoped_connection fileCleanedConnection_;

  Database *db_delete_files_ = nullptr;
  std::shared_ptr<DeletedFilesTable> deletedFilesTable_;
  std::chrono::system_clock::time_point lastSearchTime_ =
      std::chrono::system_clock::now();

  // 删除文件收集服务
  std::shared_ptr<core::DeletedFileCollectionService> deletedFileCollectionService_;

  // 统一的UI更新优化 - 合并删除文件计数和批量hits更新
  QTimer* uiUpdateTimer_;
  int lastEmittedDeletedFileCount_ = -1;
  std::vector<search::Hit> pendingHits_;
  std::mutex uiUpdateMutex_;
  static constexpr int UI_UPDATE_INTERVAL = 1000; // 优化：降低更新频率到1秒，减少CPU占用

  // 批量添加hits任务的取消标志
  std::atomic<bool> cancelBatchAddTask_{false};
};

} // namespace app

} // namespace anywhere