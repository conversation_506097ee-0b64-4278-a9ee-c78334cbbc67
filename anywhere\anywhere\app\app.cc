#include <thread>
#include <chrono>
#include <boost/thread/future.hpp>
#include <boost/thread/executor.hpp>
#include <boost/thread/executors/basic_thread_pool.hpp>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <QApplication>
#include <QLocalServer>
#include <QLocalSocket>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QCommandLineParser>
#include <QIcon>
#include <QDateTime>
#include <QThread>
#include <QProcess>
#include <QStandardPaths>
#include <QtQuick>
#include <QObject>
#include <QMessageBox>
#ifdef __linux__
#include "ext4/Ext4IndexBuilder.h"
#include <QtPlugin>
#include <unistd.h>
#include <limits.h>
#endif

#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#ifdef _WIN32
#include <Windows.h>
#include <objbase.h>
#undef max
#undef min
#include <utf8.h>
// #include "services/FileMonitor.h"
#endif

#include "crash/CrashReporter.h"
#include "core/services/ConfigService.h"
#include "core/services/SearchEngineService.h"
#include "core/services/FileContentIndexService.h"
#include "core/services/LicenseService.h"
#include "core/services/CleanFileService.h"
#include "core/services/AppHistoryService.h"
#include "core/services/PrivilegedClientService.h"
#include "core/services/DeletedFileCollectionService.h"
#include "services/FileChangeService.h"
#include "services/IncrementalSearchManager.h"
#include "privileged_comm/PrivilegedServer.h"
#include "privileged_comm/PrivilegedClient.h"
#include "core/services/EventBusService.h"
#include "services/QtMainThreadExecutor.h"
#include "services/IOThreadExecutor.h"
// #include "services/Manual.h"
#include "services/Executors.h"
#include "services/Miscs.h"
#include "services/QtResourceReader.h"
#include "services/FileIconProvider.h"
#include "services/RestartManager.h"
#include "services/ResourceMonitor.h"
#include "services/Daemon.h"
#include "clean/DefaultFileCleanEngineFactory.h"
//#include "utils/InstanceLock.h"
#include "services/TikaServices.h"
#ifdef _WIN32
#include "utils/d3dfix.h"
#include "privileged_comm/PrivilegedServerWin.h"
#include "privileged_comm/PrivilegedClientWin.h"
#endif
#include "CancellationToken.h"
#include "QueryParser.h"
#include "Highlighter.h"
#include "parser/DefaultFileParseEngine.h"
#include "parser/DefaultFileFormatDetector.h"
#include "Log.h"

#include "windows/QTopHits.h"
#include "windows/QDocSet.h"
#include "windows/QTextItem.h"
#include "windows/QPreviewItem.h"
#include "windows/SkyFileMenu.h"
#include "windows/SkyUi.h"

#include "windows/fileContent/ViewModel.h"
#include "windows/fileContent/DeletedFileViewModel.h"
#include "windows/fileContent/ContentPreviewViewModel.h"
#include "windows/fileContent/DeletedContentPreviewViewModel.h"
#include "windows/fileContent/PendingFilesTableModel.h"
#include "windows/fileContent/SearchResultTableModel.h"
#include "windows/fileContent/PaginatedSearchResultTableModel.h"
#include "windows/appHistory/AppHistoryViewModel.h"
#include "windows/appHistory/AppHistoryPreviewViewModel.h"
#include "windows/clean/CleanAppHistoryViewModel.h"
#include "windows/setting/SettingModel.h"
#include "windows/clean/CleanConfirmViewModel.h"
#include "windows/clean/CleanFilesViewModel.h"
#include "windows/license/LicenseViewModel.h"
#include "windows/root/RootViewModel.h"
#include "windows/QEventBus.h"
#include "windows/QLicense.h"
#include "windows/QMatchData.h" 
#include "windows/QSnippet.h"
#include "windows/QTrayIcon.h"
#include "windows/QLockScreen.h"
#include "Utils.h"
#include "services/NetworkService.h"
#include "services/TaskManager.h"

#ifdef __linux__
#include <cstdlib>

#include <sys/stat.h>
#include <sys/mman.h>
#include <unistd.h>
#include "privileged_comm/PrivilegedServerUnix.h"
#include "privileged_comm/PrivilegedClientUnix.h"
#endif

using namespace anywhere::app;
using namespace anywhere::core;
using namespace anywhere;

static void initializeLog(bool verbose) {
  // 为服务进程使用不同的日志文件，避免冲突
  #ifndef JMA_QUICKSEARCH_SINGLE_VERSION
  std::string logFileName = "anywhere_net.log";
  #else
  std::string logFileName = "anywhere.log";
  #endif
  auto logFilePath = std::filesystem::temp_directory_path() / logFileName;
  auto file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>(
      logFilePath.string(), true);

  auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();

  std::vector<spdlog::sink_ptr> sinks = {console_sink, file_sink};

  auto logger = std::make_shared<spdlog::logger>("multi_sink", sinks.begin(),
                                                 sinks.end());

  spdlog::set_default_logger(logger);
  // @@@@ liukai  log level
  spdlog::set_level(verbose ? spdlog::level::debug : spdlog::level::debug);

  spdlog::flush_every(std::chrono::seconds(5));
}

static void registerQmlTypes() {
  // qmlRegisterType<QTrayIcon>("AnywhereQmlModule", 1, 0, "QTrayIcon");
  qmlRegisterUncreatableType<QLicense>("AnywhereQmlModule", 1, 0, "QLicense",
                                       "QLicense can't be created in qml");
  qRegisterMetaType<QLicense::LicenseLevel>("QLicense::LicenseLevel");
  qRegisterMetaType<QLicense::LicenseType>("QLicense::LicenseType");
  qRegisterMetaType<QHitKeyword>("QHitKeyword");
  qRegisterMetaType<QMatchData>("QMatchData");
  qRegisterMetaType<QSnippet>("QSnippet");
  qRegisterMetaType<ViewModel::IndexState>("MainWindowViewModel::IndexState");
  qmlRegisterType<ViewModel>("AnywhereQmlModule", 1, 0, "MainWindowViewModel");
  qmlRegisterType<DeletedFileViewModel>("AnywhereQmlModule", 1, 0, "DeletedFileViewModel");
  qRegisterMetaType<CleanConfirmViewModel::State>(
      "CleanConfirmWindowViewModel::State");
  qmlRegisterType<CleanConfirmViewModel>("AnywhereQmlModule", 1, 0,
                                         "CleanConfirmWindowViewModel");
  qRegisterMetaType<CleanFilesViewModel::State>(
      "CleanFilesWindowViewModel::State");
  qmlRegisterType<CleanFilesViewModel>("AnywhereQmlModule", 1, 0,
                                       "CleanFilesWindowViewModel");
  qRegisterMetaType<CleanAppHistoryViewModel::State>(
      "CleanAppHistoryWindowViewModel::State");
  qmlRegisterType<CleanAppHistoryViewModel>("AnywhereQmlModule", 1, 0,
                                            "CleanAppHistoryWindowViewModel");
  qmlRegisterType<LicenseViewModel>("AnywhereQmlModule", 1, 0,
                                    "LicenseWindowViewModel");
  qmlRegisterType<SearchResultTableModel>("AnywhereQmlModule", 1, 0,
                                          "SearchResultTableModel");
  qmlRegisterType<PaginatedSearchResultTableModel>("AnywhereQmlModule", 1, 0,
                                          "PaginatedSearchResultTableModel");
  qmlRegisterType<PendingFilesTableModel>("AnywhereQmlModule", 1, 0,
                                          "PendingFilesTableModel");
  qRegisterMetaType<ContentPreviewViewModel::LoadState>(
      "ContentPreviewViewModel::LoadState");
  qmlRegisterType<ContentPreviewViewModel>("AnywhereQmlModule", 1, 0,
                                           "ContentPreviewViewModel");
  qRegisterMetaType<ContentPreviewViewModel::LoadState>(
          "DeletedContentPreviewViewModel::LoadState");
  qmlRegisterType<DeletedContentPreviewViewModel>("AnywhereQmlModule", 1, 0,
                                           "DeletedContentPreviewViewModel");
  qmlRegisterType<RootViewModel>("AnywhereQmlModule", 1, 0, "RootViewModel");
  qmlRegisterType<SettingModel>("AnywhereQmlModule", 1, 0, "SettingModel");
  qRegisterMetaType<SettingModel::QFileFormat>("SettingModel::QFileFormat");
  qRegisterMetaType<SettingModel::QFontSize>("SettingModel::QFontSize");

  qmlRegisterType<AppHistoryViewModel>("AnywhereQmlModule", 1, 0,
                                       "AppHistoryViewModel");
  qRegisterMetaType<AppHistoryViewModel::IndexState>(
      "AppHistoryViewModel::IndexState");

  qRegisterMetaType<AppHistoryPreviewViewModel::LoadState>(
      "AppHistoryPreviewViewModel::LoadState");
  qmlRegisterType<AppHistoryPreviewViewModel>("AnywhereQmlModule", 1, 0,
                                              "AppHistoryPreviewViewModel");

  qmlRegisterType<SkyUi::SkyFileMenu>("SkyUi", 1, 0, "SkyFileMenu");
  // qmlRegisterType<QLockScreen>("AnywhereQmlModule", 1, 0, "QLockScreen");
  qmlRegisterType<SkyUi::SkyModifierWatcher>("SkyUi", 1, 0,
                                             "SkyModifierWatcher");
  qmlRegisterUncreatableType<QTopHits>("AnywhereQmlModule", 1, 0, "QTopHits",
                                       "QTopHits can't be created in qml");
  qmlRegisterUncreatableType<QDocSet>("AnywhereQmlModule", 1, 0, "QDocSet",
                                      "QDocSet can't be created in qml");
  qmlRegisterUncreatableMetaObject(SkyUi::staticMetaObject, "SkyUi", 1, 0,
                                   "SkyIcons", "Access to enums & flags only");
  qRegisterMetaType<QLineData::LineState>("QTextItem::LineState");
  qmlRegisterType<anywhere::app::QTextItem>("AnywhereQmlModule", 1, 0,
                                            "QTextItem");
  qmlRegisterType<QPreviewItem>("AnywhereQmlModule", 1, 0, "QPreviewItem");
}

static void createSocketServer(QString socketPath) {
  std::unique_ptr<privileged_comm::PrivilegedServer> privilegedServer;
#ifdef _WIN32
  std::u8string socketPathU8 =
      reinterpret_cast<const char8_t *>(socketPath.toUtf8().constData());
  privilegedServer =
      std::make_unique<privileged_comm::PrivilegedServerWin>(socketPathU8);
#else
  privilegedServer = std::make_unique<privileged_comm::PrivilegedServerUnix>(
      socketPath.toStdString());
#endif
  privilegedServer->runForever();
}

QString getExecutablePath() {
#ifdef _WIN32
  wchar_t result[MAX_PATH];
  GetModuleFileNameW(NULL, result, MAX_PATH);
  auto wresult = std::wstring(result);
  std::u8string path;
  utf8::utf16to8(wresult.begin(), wresult.end(), std::back_inserter(path));
  return QString::fromUtf8(reinterpret_cast<const char *>(path.c_str()));
#else
  char result[PATH_MAX];
  auto len = readlink("/proc/self/exe", result, PATH_MAX - 1);
  if (len == -1) {
    throw std::runtime_error(
        fmt::format("get executable path failed: {}", strerror(errno)));
  }

  result[len] = '\0';
  SPDLOG_DEBUG("executable path: {}", result);
  return QString::fromLocal8Bit(result, -1);
#endif
}

/**
 * @brief 进程启动后 需要执行的任务
 * @param argc 参数计数
 * @param argv 参数列表
 * @param appPath 应用程序路径
 * @param arguments 启动参数
 * @param resourceMonitor 指针监听器
 *
 * @return 是否需要重启
 *      -> 提权成功 -> 进入loading界面等成程序加载完成 -> 加载主界面 -> 手动关闭程序 -> 注销服务
 *     丨
 * 启动提权窗口
 *     |
 *     -> 提权失败 -> 跳过loading/主界面 -> 注销服务
 */
bool doJob(int argc, char *argv[], const QStringList &arguments,
           std::shared_ptr<ResourceMonitor> &resourceMonitor) {

  QCommandLineParser parser;

  parser.addVersionOption();
  QCommandLineOption debugOption("debug", "");
  parser.addOption(debugOption);
  QCommandLineOption pathOption("path", "", "path");
  parser.addOption(pathOption);
  QCommandLineOption socketPathOption("socket-path", "", "socket-path");
  parser.addOption(socketPathOption);
  QCommandLineOption verboseOption("verbose");
  parser.addOption(verboseOption);
  QCommandLineOption skipKysecOption("skip-kysec");
  parser.addOption(skipKysecOption);

  parser.process(arguments);

  // 创建socket server
  if (parser.isSet(socketPathOption)) {
    SPDLOG_INFO("start socket server");
    auto socketServerPath = parser.value(socketPathOption);
    createSocketServer(socketServerPath);
    return false;
  }
#ifdef __linux__
  // kylin spx 直接设置 安全标记为 trusted 因为只设置一个
  // 然后添加启动参数 --skip-kysec
  if (Utils::getSystemName() == Utils::SYSTEM_NAME::KYLIN_SP &&
      !parser.isSet(skipKysecOption)) {
    auto appPath = getExecutablePath();
    if (!Utils::spGetKysecIsTrusted(appPath.toStdString())) {
      if (!Utils::spSetKysec(appPath.toStdString())) {
        // 设置失败也没有重启的必要了 直接运行吧!
        SPDLOG_ERROR("set kysec failed!");
      } else {
        return true;
      }
    } else {
      SPDLOG_INFO("kysec is trusted");
    }
  }

  Q_IMPORT_PLUGIN(QComposePlatformInputContextPlugin)
  Q_IMPORT_PLUGIN(QIbusPlatformInputContextPlugin)
  Q_IMPORT_PLUGIN(QFcitx5PlatformInputContextPlugin)

  // 在 linux 下, 我们需要设置 umask 为 0, 这样才能创建指定权限的文件,
  // 否则创建的文件权限会被 umask 修改
  umask(0);
#endif

  // 只有在主进程中才会初始化日志, socket server 不会初始化日志
  // initializeLog(parser.isSet(verboseOption));
  // 创建 CrashReporter, 当程序崩溃时, 它会制作 minidump 文件
  ::anywhere::crash::CrashReporter crashReporter;

  // 启用高分屏缩放支持
  QGuiApplication::setAttribute(Qt::AA_EnableHighDpiScaling);

  QApplication app(argc, argv);
  // 改进的单实例检查，使用全局命名的互斥量
  #ifdef JMA_QUICKSEARCH_SINGLE_VERSION
  HANDLE hMutex = CreateMutexW(NULL, FALSE, L"Global\\AnywhereUniqueInstance");
  #else
  HANDLE hMutex = CreateMutexW(NULL, FALSE, L"Global\\AnywhereUniqueInstanceNet");
  #endif
  if (hMutex == NULL || GetLastError() == ERROR_ALREADY_EXISTS) {
    #ifdef JMA_QUICKSEARCH_SINGLE_VERSION
    QMessageBox::warning(nullptr, "提示", "该程序已经在运行");
    #endif
    if (hMutex) CloseHandle(hMutex);
    exit(0);
  }

   //FileMonitor::instance(2).start();
  // Create single tray icon instance
  
  app.setWindowIcon(QIcon(":./qml/Assets/Images/icon.ico"));

  QCoreApplication::setApplicationName("anywhere");
  QCoreApplication::setApplicationVersion("1.0");
  /*auto instanceLock = std::make_shared<anywhere::app::InstanceLock>();
  if (!instanceLock->acquire()) {
    SPDLOG_INFO("instance already running");
    return false;
  }*/

  auto configService = std::make_shared<ConfigService>();
  resourceMonitor->addResource(configService);

  auto tmpDir = std::filesystem::temp_directory_path();

  // 创建socket client
  auto clientPath = QCoreApplication::applicationFilePath();
  privileged_comm::PrivilegedClientConfig privilegedClientConfig(
      parser.isSet(verboseOption));
  std::shared_ptr<privileged_comm::PrivilegedClient> privilegedClientService;

#ifdef _WIN32
  std::u8string u8clientPath =
      reinterpret_cast<const char8_t *>(clientPath.toUtf8().constData());
  privilegedClientService =
      std::make_shared<privileged_comm::PrivilegedClientWin>(
          u8clientPath, tmpDir, privilegedClientConfig);
#else
  privilegedClientService =
      std::make_shared<privileged_comm::PrivilegedClientUnix>(
          clientPath.toStdString(), tmpDir, configService->enabledSuexec(),
          privilegedClientConfig);
#endif

  resourceMonitor->addResource(privilegedClientService);
  auto mainThreadExecutor = std::make_shared<
      boost::executors::executor_adaptor<QtMainThreadExecutor>>();
  resourceMonitor->addResource(mainThreadExecutor);

  auto ioExecutor = std::make_shared<
      boost::executors::executor_adaptor<anywhere::app::basic_thread_pool>>(
      // std::thread::hardware_concurrency() + 1
      // 临时使用
      16, [](const auto &) {
#ifdef _WIN32
        // windows ifilters 需要初始化 COM 套间, 因此我们给所有的 IO 线程初始化
        // COM 套间
        CoInitializeEx(nullptr, COINIT_MULTITHREADED);
#else
#endif
      });
  resourceMonitor->addResource(ioExecutor);
  auto resourceReader = std::make_shared<QtResourceReader>();
  resourceMonitor->addResource(resourceReader);
#ifdef _WIN32
  fixD3dCompilerIssue(resourceReader.get(), tmpDir);
#endif

  // 先注册 PrivilegedClientServiceToken 服务 因为在 RootWindow 中有使用
  ServiceRegistry::instance()->registerService<PrivilegedClientServiceToken>(
      privilegedClientService);
  QCoreApplication::processEvents();

  // 创建engine实例
  QQmlApplicationEngine engine;

  // Register QML types with tray icon context property
  registerQmlTypes();
  auto trayIcon = QTrayIcon::instance();
  auto lockScreen = QLockScreen::instance();
  #ifndef JMA_QUICKSEARCH_SINGLE_VERSION
  lockScreen->EnableDebugPrivilege();
  #endif
  engine.rootContext()->setContextProperty("trayIcon", trayIcon);
  engine.rootContext()->setContextProperty("lockScreen", lockScreen);
  engine.addImportPath("qrc:/qml/");
  // engine.load(QUrl::fromLocalFile("qrc:/qml/LockScreen.qml"));
  // 提权界面
#ifdef __linux__
  if (configService->rootWindowEnabled()) {
    if (parser.isSet(debugOption)) {
      if (!parser.isSet(pathOption)) {
        engine.load(QUrl::fromLocalFile("qrc:/qml/RootWindow.qml"));
      }
    } else {
      engine.load(QUrl(QStringLiteral("qrc:/qml/RootWindow.qml")));
    }
    // 阻塞提权界面直到提权成功或者用户退出登录
    QQuickWindow *rootWindow = nullptr;

    while (true) {
      QCoreApplication::processEvents();
      if (!rootWindow) {
        for (QObject *obj : engine.rootObjects()) {
          if (auto window = qobject_cast<QQuickWindow *>(obj)) {
            if (window->objectName() == "rootWindow") {
              rootWindow = window;
              break;
            }
          }
        }
      }
      if (rootWindow && rootWindow->property("closed").toBool()) {
        if (!rootWindow->property("isRoot").toBool()) {
          SPDLOG_WARN("get root failed");
          ServiceRegistry::destroy();
          return false;
        }
        break;
      }
    }
  } else {
    try {
      auto fd = privilegedClientService->open("/", O_RDONLY, 0);
      close(fd);
    } catch (const std::exception &e) {
      SPDLOG_ERROR("try root access failed: {}", e.what());
      ServiceRegistry::destroy();
      return false;
    }
  }
#endif
  anywhere::services::TikaServices::instance().start();
  // 记录下程序加载界面展示时间
  qint64 splashStartScreenTime = QDateTime::currentMSecsSinceEpoch();

  auto eventBusService = std::make_shared<EventBusService>();
  resourceMonitor->addResource(eventBusService);

  std::filesystem::path currentLibPath = std::filesystem::path();
  if (!configService->isPortableVersion()) {
    currentLibPath = std::filesystem::current_path() / u8"lib";
  }
  auto licenseService = std::make_shared<LicenseService>(
      tmpDir, currentLibPath, resourceReader, ioExecutor,
      privilegedClientService, configService->enableEmptyMachineCode());
  resourceMonitor->addResource(licenseService);
  auto fileIconProvider = std::make_shared<FileIconProvider>();
  resourceMonitor->addResource(fileIconProvider);
  auto restartManager = std::make_shared<RestartManager>();
  resourceMonitor->addResource(restartManager);
  // auto manual = std::make_shared<Manual>();
  //resourceMonitor->addResource(manual);
  SPDLOG_INFO("Creating SearchEngineService...");
  auto searchEngineService = std::make_shared<SearchEngineService>(
      configService, eventBusService, mainThreadExecutor, ioExecutor);
  resourceMonitor->addResource(searchEngineService);
  SPDLOG_INFO("SearchEngineService created");
  auto defaultFileParseEngine =
      std::make_shared<parser::DefaultFileParseEngine>(
          tmpDir, currentLibPath, resourceReader,
          configService->enabledFileFormats(),
          configService->maxCharactorsPerFile());
  resourceMonitor->addResource(defaultFileParseEngine);
  SPDLOG_INFO("Creating FileContentIndexService...");
  auto fileContentIndexService = std::make_shared<FileContentIndexService>(
      configService, searchEngineService, defaultFileParseEngine,
      eventBusService, mainThreadExecutor, ioExecutor);
  resourceMonitor->addResource(fileContentIndexService);
  SPDLOG_INFO("FileContentIndexService created");
  auto queryParser = std::make_shared<QueryParser>();
  resourceMonitor->addResource(queryParser);
  auto fileFormatDetector = std::make_shared<parser::DefaultFileFormatDetector>(
      tmpDir, resourceReader);
  resourceMonitor->addResource(fileFormatDetector);
  auto highlighterFactory = std::make_shared<HighlighterFactory>();
  resourceMonitor->addResource(highlighterFactory);
  auto cleanFileEngineFactory =
      std::make_shared<clean::DefaultFileCleanEngineFactory>();
  resourceMonitor->addResource(cleanFileEngineFactory);
  auto cleanFileService = std::make_shared<CleanFileService>(
      configService, searchEngineService, cleanFileEngineFactory,
      mainThreadExecutor, ioExecutor, privilegedClientService);
  resourceMonitor->addResource(cleanFileService);
  auto appHistoryService = std::make_shared<AppHistoryService>(
      configService, mainThreadExecutor, ioExecutor, privilegedClientService);
  resourceMonitor->addResource(appHistoryService);
  #ifdef JMA_QUICKSEARCH_SINGLE_VERSION
  // 启动时预加载getAppHistory到缓存
  SPDLOG_INFO("Preloading AppHistory data...");
  appHistoryService->getAppHistory();
  SPDLOG_INFO("AppHistory preload initiated");
  #endif
  auto networkService = std::make_shared<NetworkService>(configService);
  resourceMonitor->addResource(networkService);
  auto deletedFileCollectionService = std::make_shared<DeletedFileCollectionService>();
  resourceMonitor->addResource(deletedFileCollectionService);
  QCoreApplication::processEvents();
  ServiceRegistry::instance()->registerService<PrivilegedClientServiceToken>(
      privilegedClientService);
  //ServiceRegistry::instance()->registerService<InstanceLockToken>(instanceLock);
  ServiceRegistry::instance()->registerService<LicenseServiceToken>(
      licenseService);
  ServiceRegistry::instance()->registerService<ConfigServiceToken>(
      configService);
  ServiceRegistry::instance()->registerService<EventBusServiceToken>(
      eventBusService);
  ServiceRegistry::instance()->registerService<SearchEngineServiceToken>(
      searchEngineService);
  ServiceRegistry::instance()->registerService<FileContentIndexServiceToken>(
      fileContentIndexService);
  ServiceRegistry::instance()->registerService<MainExecutorToken>(
      mainThreadExecutor);
  ServiceRegistry::instance()->registerService<IoExecutorToken>(ioExecutor);
  ServiceRegistry::instance()->registerService<QueryParserToken>(queryParser);
  ServiceRegistry::instance()->registerService<FileFormatDetectorToken>(
      fileFormatDetector);
  ServiceRegistry::instance()->registerService<HighlighterFactoryToken>(
      highlighterFactory);
  ServiceRegistry::instance()->registerService<ResourceReaderToken>(
      resourceReader);
  ServiceRegistry::instance()->registerService<FileIconProviderToken>(
      fileIconProvider);
  ServiceRegistry::instance()->registerService<RestartManagerToken>(
      restartManager);
  // ServiceRegistry::instance()->registerService<ManualToken>(manual);
  ServiceRegistry::instance()->registerService<DefaultFileParseEngineToken>(
      defaultFileParseEngine);
  ServiceRegistry::instance()->registerService<CleanFileServiceToken>(
      cleanFileService);
  ServiceRegistry::instance()->registerService<AppHistoryServiceToken>(
      appHistoryService);
  ServiceRegistry::instance()->registerService<NetworkServiceToken>(
      networkService);
  ServiceRegistry::instance()->registerService<DeletedFileCollectionServiceToken>(
      deletedFileCollectionService);

  // 启动删除文件收集服务
  #ifdef JMA_QUICKSEARCH_SINGLE_VERSION
  deletedFileCollectionService->start();
  #endif
  #ifndef JMA_QUICKSEARCH_SINGLE_VERSION
  // 启动文件变更监控服务
  auto& fileChangeService = anywhere::app::FileChangeService::instance();
  // 设置文件格式检测器
  fileChangeService.setFileFormatDetector(fileFormatDetector);
  if (fileChangeService.start()) {
    SPDLOG_INFO("File change monitoring service started successfully");
  } else {
    SPDLOG_ERROR("Failed to start file change monitoring service");
  }
  
  // 启动增量检索管理器
  auto* incrementalSearchManager = anywhere::app::IncrementalSearchManager::instance();
  incrementalSearchManager->start();
  SPDLOG_INFO("Incremental search manager started");
  #endif
  QCoreApplication::processEvents();

#ifdef __linux__
  ext4::Ext4IndexBuilder::initialize(privilegedClientService);
#endif

  QEventBus myEventBus;

  engine.rootContext()->setContextProperty("eventBus", &myEventBus);
  if (parser.isSet(debugOption)) {
    if (parser.isSet(pathOption)) {
      engine.load(QUrl::fromLocalFile(parser.value(pathOption)));
    } else {
      engine.load(QUrl::fromLocalFile("qrc:/qml/MainWindow.qml"));
    }
  } else {
    engine.load(QUrl(QStringLiteral("qrc:/qml/MainWindow.qml")));
  }
  #ifdef JMA_QUICKSEARCH_SINGLE_VERSION
      trayIcon->showWindow();
  #endif
  QCoreApplication::processEvents();
  QLockScreen::instance()->lock(true);
  QCoreApplication::processEvents();
  #ifndef JMA_QUICKSEARCH_SINGLE_VERSION
   // 程序加载闪屏界面
   if (parser.isSet(debugOption)) {
    if (!parser.isSet(pathOption)) {
      engine.load(QUrl::fromLocalFile("qrc:/qml/SplashScreen.qml"));
    }
  } else {
    engine.load(QUrl(QStringLiteral("qrc:/qml/SplashScreen.qml")));
  }
  QCoreApplication::processEvents();
  //加载锁屏界面

  QQuickWindow *splashScreen = nullptr;

  // 当主进程结束时 关闭 加载界面 我们通过界面增加 objectName: splashScreen 标识
  // 来确定加载界面
  for (QObject *obj : engine.rootObjects()) {
    if (auto window = qobject_cast<QQuickWindow *>(obj)) {
      if (window->objectName() == "splashScreen") {
        splashScreen = window;
        break;
      }
    }
  }
  
  auto differenceTime =
      QDateTime::currentMSecsSinceEpoch() - splashStartScreenTime;
  if (differenceTime < 5000) {
    // 过 1s 后, 关闭加载界面
    for (auto i = 0; i < 5000 - differenceTime; i += 100) {
      QThread::msleep(100);
      QCoreApplication::processEvents();
    }
    QThread::msleep(differenceTime % 100);
    QCoreApplication::processEvents();
  }
  if (splashScreen) {
    splashScreen->close();
    splashScreen->deleteLater();
  }
  QCoreApplication::processEvents();

  #endif

  // Initialize network service
  #ifndef JMA_QUICKSEARCH_SINGLE_VERSION
  ServiceRegistry::instance()->getService<NetworkServiceToken>()->startHeartbeat();
  TaskManager::instance()->start();
  #endif
  app.exec();
  #ifndef JMA_QUICKSEARCH_SINGLE_VERSION
  ServiceRegistry::instance()->getService<NetworkServiceToken>()->stopHeartbeat();
  TaskManager::instance()->stop();
  #endif
  searchEngineService->cancel();

  while (true) {
    QCoreApplication::processEvents();
    if (ioExecutor->underlying_executor().empty() &&
        mainThreadExecutor->underlying_executor().empty()) {
      SPDLOG_INFO("close ioExecutor");
      ioExecutor->underlying_executor().close();
      // ioExecutor->underlying_executor().join();
      mainThreadExecutor->close();
      break;
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
  #ifndef JMA_QUICKSEARCH_SINGLE_VERSION
  // 停止增量检索管理器
  anywhere::app::IncrementalSearchManager::instance()->stop();
  SPDLOG_INFO("Incremental search manager stopped");


  // 停止文件变更监控服务
  anywhere::app::FileChangeService::instance().stop();
  SPDLOG_INFO("File change monitoring service stopped");
  #endif

  ServiceRegistry::destroy();

  return restartManager->isRestartRequested();
}

int main(int argc, char *argv[]) {
  // 检查命令行参数
  QStringList arguments;
  for (int i = 0; i < argc; ++i) {
    arguments.push_back(QString::fromLocal8Bit(argv[i], -1));
  }
  initializeLog(false);

  QGuiApplication::setQuitOnLastWindowClosed(false);
  // Normal application execution

// #ifdef _WIN32
//   DlpHookCtrl dlpHookCtrl;
//   if (dlpHookCtrl.Start()) {
//     SPDLOG_INFO("DlpHookCtrl Start");
//   }
//   else {
//     SPDLOG_ERROR("DlpHookCtrl start failed");
//     return -1;
//   }
// #endif
  auto resourceMonitor = std::make_shared<ResourceMonitor>();
  bool restart = doJob(argc, argv, arguments, resourceMonitor);
  if (!resourceMonitor.get()->ensureResourcesReleased()) {
    SPDLOG_INFO("check if all are released");
    return -1;
  }

  auto tmpDir = std::filesystem::temp_directory_path();
  auto anywhereCachePath = tmpDir / "anywhere";
  if (std::filesystem::exists(anywhereCachePath)) {
    if (std::filesystem::remove_all(anywhereCachePath)) {
      SPDLOG_DEBUG(
          "removed {} success!",
          reinterpret_cast<const char *>(anywhereCachePath.u8string().c_str()));
    } else {
      SPDLOG_ERROR(
          "removed {} failed!",
          reinterpret_cast<const char *>(anywhereCachePath.u8string().c_str()));
    }
  }
  if (restart) {
    QString appPath;
    try {
      appPath = getExecutablePath();
    } catch (std::exception &e) {
      SPDLOG_ERROR("get app path failed: {}", e.what());
    }
#ifdef __linux__
    if (Utils::getSystemName() == Utils::SYSTEM_NAME::KYLIN_SP) {
      // kylin spx 在重启时添加 --skip-kysec 参数 否则可能会死循环
      arguments.push_back("--skip-kysec");
    }
#endif
    bool success = QProcess::startDetached(appPath, arguments);

    if (success) {
      SPDLOG_INFO("try to restart success!");
    } else {
      SPDLOG_ERROR("try to restart error!");
    }
  }

  return 0;
}
